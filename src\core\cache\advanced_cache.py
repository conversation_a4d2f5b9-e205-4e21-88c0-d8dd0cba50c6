"""
高级本地缓存优化系统

实现高效的上下文预处理和持久化存储，支持压缩、索引和智能预取。
"""

import logging
import sqlite3
import pickle
import lz4.frame
import hashlib
import json
import os
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import threading
from collections import OrderedDict
import numpy as np


@dataclass
class CacheMetadata:
    """缓存元数据"""
    key: str
    size: int
    compressed_size: int
    created_at: datetime
    last_accessed: datetime
    access_count: int
    compression_ratio: float
    tags: Dict[str, str] = field(default_factory=dict)
    
    @property
    def age_hours(self) -> float:
        """获取缓存年龄（小时）"""
        return (datetime.now() - self.created_at).total_seconds() / 3600
    
    @property
    def priority_score(self) -> float:
        """计算优先级评分"""
        # 基于访问频率、最近访问时间和压缩效果
        frequency_score = min(self.access_count / 10.0, 1.0)
        recency_score = max(0, 1.0 - self.age_hours / 168)  # 一周内
        compression_score = 1.0 - self.compression_ratio
        
        return (frequency_score * 0.4 + recency_score * 0.4 + compression_score * 0.2)


class CompressionEngine:
    """压缩引擎"""
    
    def __init__(self):
        """初始化压缩引擎"""
        self.logger = logging.getLogger(__name__)
    
    def compress(self, data: bytes) -> Tuple[bytes, float]:
        """
        压缩数据
        
        Args:
            data: 原始数据
            
        Returns:
            (压缩后数据, 压缩比)
        """
        try:
            compressed = lz4.frame.compress(data)
            compression_ratio = len(compressed) / len(data) if len(data) > 0 else 1.0
            return compressed, compression_ratio
        except Exception as e:
            self.logger.warning(f"Compression failed: {e}")
            return data, 1.0
    
    def decompress(self, compressed_data: bytes) -> bytes:
        """
        解压缩数据
        
        Args:
            compressed_data: 压缩数据
            
        Returns:
            原始数据
        """
        try:
            return lz4.frame.decompress(compressed_data)
        except Exception as e:
            self.logger.warning(f"Decompression failed: {e}")
            return compressed_data


class CacheIndex:
    """缓存索引"""
    
    def __init__(self, db_path: str):
        """
        初始化缓存索引
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.lock = threading.Lock()
        self._init_database()
        self.logger = logging.getLogger(__name__)
    
    def _init_database(self):
        """初始化数据库"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS cache_index (
                    key TEXT PRIMARY KEY,
                    size INTEGER,
                    compressed_size INTEGER,
                    created_at TEXT,
                    last_accessed TEXT,
                    access_count INTEGER,
                    compression_ratio REAL,
                    tags TEXT,
                    file_path TEXT
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_last_accessed ON cache_index(last_accessed)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_access_count ON cache_index(access_count)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_size ON cache_index(size)')
            
            conn.commit()
    
    def add_entry(self, metadata: CacheMetadata, file_path: str):
        """添加缓存条目"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO cache_index 
                    (key, size, compressed_size, created_at, last_accessed, 
                     access_count, compression_ratio, tags, file_path)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    metadata.key,
                    metadata.size,
                    metadata.compressed_size,
                    metadata.created_at.isoformat(),
                    metadata.last_accessed.isoformat(),
                    metadata.access_count,
                    metadata.compression_ratio,
                    json.dumps(metadata.tags),
                    file_path
                ))
                conn.commit()
    
    def get_entry(self, key: str) -> Optional[Tuple[CacheMetadata, str]]:
        """获取缓存条目"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT * FROM cache_index WHERE key = ?', (key,)
                )
                row = cursor.fetchone()
                
                if row:
                    metadata = CacheMetadata(
                        key=row[0],
                        size=row[1],
                        compressed_size=row[2],
                        created_at=datetime.fromisoformat(row[3]),
                        last_accessed=datetime.fromisoformat(row[4]),
                        access_count=row[5],
                        compression_ratio=row[6],
                        tags=json.loads(row[7]) if row[7] else {}
                    )
                    return metadata, row[8]
                
                return None
    
    def update_access(self, key: str):
        """更新访问信息"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    UPDATE cache_index 
                    SET last_accessed = ?, access_count = access_count + 1
                    WHERE key = ?
                ''', (datetime.now().isoformat(), key))
                conn.commit()
    
    def get_entries_by_priority(self, limit: int = 100) -> List[Tuple[CacheMetadata, str]]:
        """按优先级获取条目"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT * FROM cache_index 
                    ORDER BY access_count DESC, last_accessed DESC
                    LIMIT ?
                ''', (limit,))
                
                entries = []
                for row in cursor.fetchall():
                    metadata = CacheMetadata(
                        key=row[0],
                        size=row[1],
                        compressed_size=row[2],
                        created_at=datetime.fromisoformat(row[3]),
                        last_accessed=datetime.fromisoformat(row[4]),
                        access_count=row[5],
                        compression_ratio=row[6],
                        tags=json.loads(row[7]) if row[7] else {}
                    )
                    entries.append((metadata, row[8]))
                
                return entries
    
    def cleanup_expired(self, max_age_hours: int = 168):  # 7天
        """清理过期条目"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT key, file_path FROM cache_index WHERE created_at < ?',
                    (cutoff_time.isoformat(),)
                )
                
                expired_entries = cursor.fetchall()
                
                # 删除文件
                for key, file_path in expired_entries:
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                    except Exception as e:
                        self.logger.warning(f"Failed to remove cache file {file_path}: {e}")
                
                # 删除索引记录
                conn.execute(
                    'DELETE FROM cache_index WHERE created_at < ?',
                    (cutoff_time.isoformat(),)
                )
                conn.commit()
                
                return len(expired_entries)


class AdvancedCacheManager:
    """高级缓存管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化高级缓存管理器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        cache_config = self.config.get('cache', {})
        
        # 配置参数
        self.cache_dir = Path('data/advanced_cache')
        self.index_db = 'data/cache_index.db'
        self.max_cache_size_mb = cache_config.get('max_cache_size_mb', 1000)
        self.max_memory_cache_items = cache_config.get('max_memory_items', 100)
        
        # 确保目录存在
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 核心组件
        self.compression_engine = CompressionEngine()
        self.index = CacheIndex(self.index_db)
        
        # 内存缓存（LRU）
        self.memory_cache: OrderedDict[str, Any] = OrderedDict()
        self.memory_cache_lock = threading.Lock()
        
        # 预取队列
        self.prefetch_queue: Set[str] = set()
        self.prefetch_lock = threading.Lock()
        
        self.logger = logging.getLogger(__name__)
    
    def put(self, key: str, data: Any, tags: Optional[Dict[str, str]] = None) -> bool:
        """
        存储数据到缓存
        
        Args:
            key: 缓存键
            data: 数据
            tags: 标签
            
        Returns:
            是否成功
        """
        try:
            # 序列化数据
            serialized = pickle.dumps(data)
            
            # 压缩数据
            compressed, compression_ratio = self.compression_engine.compress(serialized)
            
            # 生成文件路径
            file_hash = hashlib.md5(key.encode()).hexdigest()
            file_path = self.cache_dir / f"{file_hash}.cache"
            
            # 写入文件
            with open(file_path, 'wb') as f:
                f.write(compressed)
            
            # 创建元数据
            metadata = CacheMetadata(
                key=key,
                size=len(serialized),
                compressed_size=len(compressed),
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=0,
                compression_ratio=compression_ratio,
                tags=tags or {}
            )
            
            # 添加到索引
            self.index.add_entry(metadata, str(file_path))
            
            # 添加到内存缓存
            self._add_to_memory_cache(key, data)
            
            # 检查缓存大小限制
            self._enforce_size_limits()
            
            self.logger.debug(f"Cached {key} with compression ratio {compression_ratio:.2f}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cache {key}: {e}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """
        从缓存获取数据
        
        Args:
            key: 缓存键
            
        Returns:
            缓存数据或None
        """
        # 首先检查内存缓存
        with self.memory_cache_lock:
            if key in self.memory_cache:
                # 移到末尾（LRU）
                data = self.memory_cache.pop(key)
                self.memory_cache[key] = data
                self.index.update_access(key)
                return data
        
        # 检查磁盘缓存
        entry = self.index.get_entry(key)
        if not entry:
            return None
        
        metadata, file_path = entry
        
        try:
            # 读取文件
            with open(file_path, 'rb') as f:
                compressed_data = f.read()
            
            # 解压缩
            decompressed = self.compression_engine.decompress(compressed_data)
            
            # 反序列化
            data = pickle.loads(decompressed)
            
            # 更新访问信息
            self.index.update_access(key)
            
            # 添加到内存缓存
            self._add_to_memory_cache(key, data)
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve {key}: {e}")
            return None
    
    def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功
        """
        # 从内存缓存删除
        with self.memory_cache_lock:
            self.memory_cache.pop(key, None)
        
        # 从磁盘删除
        entry = self.index.get_entry(key)
        if entry:
            metadata, file_path = entry
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                return True
            except Exception as e:
                self.logger.error(f"Failed to delete cache file {file_path}: {e}")
        
        return False
    
    def prefetch(self, keys: List[str]):
        """
        预取缓存项到内存
        
        Args:
            keys: 要预取的键列表
        """
        with self.prefetch_lock:
            self.prefetch_queue.update(keys)
        
        # 在后台线程中执行预取
        threading.Thread(target=self._execute_prefetch, daemon=True).start()
    
    def _execute_prefetch(self):
        """执行预取操作"""
        with self.prefetch_lock:
            keys_to_prefetch = list(self.prefetch_queue)
            self.prefetch_queue.clear()
        
        for key in keys_to_prefetch:
            if key not in self.memory_cache:
                self.get(key)  # 这会将数据加载到内存缓存
    
    def _add_to_memory_cache(self, key: str, data: Any):
        """添加到内存缓存"""
        with self.memory_cache_lock:
            # 如果已存在，先删除
            if key in self.memory_cache:
                del self.memory_cache[key]
            
            # 添加到末尾
            self.memory_cache[key] = data
            
            # 检查大小限制
            while len(self.memory_cache) > self.max_memory_cache_items:
                # 删除最旧的项目
                self.memory_cache.popitem(last=False)
    
    def _enforce_size_limits(self):
        """强制执行大小限制"""
        # 获取当前缓存大小
        total_size_mb = self._get_total_cache_size_mb()
        
        if total_size_mb > self.max_cache_size_mb:
            # 获取按优先级排序的条目
            entries = self.index.get_entries_by_priority(1000)
            
            # 删除优先级最低的条目
            removed_size = 0
            for metadata, file_path in reversed(entries):
                if total_size_mb - removed_size <= self.max_cache_size_mb * 0.8:
                    break
                
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        removed_size += metadata.compressed_size / (1024 * 1024)
                    
                    # 从内存缓存删除
                    with self.memory_cache_lock:
                        self.memory_cache.pop(metadata.key, None)
                        
                except Exception as e:
                    self.logger.warning(f"Failed to remove cache file {file_path}: {e}")
    
    def _get_total_cache_size_mb(self) -> float:
        """获取总缓存大小（MB）"""
        total_size = 0
        for file_path in self.cache_dir.glob('*.cache'):
            try:
                total_size += file_path.stat().st_size
            except Exception:
                pass
        
        return total_size / (1024 * 1024)
    
    def cleanup(self, max_age_hours: int = 168):
        """
        清理缓存
        
        Args:
            max_age_hours: 最大年龄（小时）
        """
        # 清理过期条目
        removed_count = self.index.cleanup_expired(max_age_hours)
        
        # 强制执行大小限制
        self._enforce_size_limits()
        
        self.logger.info(f"Cache cleanup completed, removed {removed_count} expired entries")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_size_mb = self._get_total_cache_size_mb()
        
        # 获取一些样本条目来计算平均值
        sample_entries = self.index.get_entries_by_priority(100)
        
        avg_compression_ratio = 0.0
        avg_access_count = 0.0
        
        if sample_entries:
            avg_compression_ratio = np.mean([m.compression_ratio for m, _ in sample_entries])
            avg_access_count = np.mean([m.access_count for m, _ in sample_entries])
        
        return {
            'total_size_mb': total_size_mb,
            'max_size_mb': self.max_cache_size_mb,
            'memory_cache_items': len(self.memory_cache),
            'max_memory_items': self.max_memory_cache_items,
            'disk_cache_files': len(list(self.cache_dir.glob('*.cache'))),
            'avg_compression_ratio': avg_compression_ratio,
            'avg_access_count': avg_access_count,
            'prefetch_queue_size': len(self.prefetch_queue)
        }
    
    def optimize(self):
        """优化缓存性能"""
        # 清理过期条目
        self.cleanup()
        
        # 预取热点数据
        hot_entries = self.index.get_entries_by_priority(20)
        hot_keys = [metadata.key for metadata, _ in hot_entries]
        self.prefetch(hot_keys)
        
        self.logger.info("Cache optimization completed")
