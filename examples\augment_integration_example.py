"""
Augment系统集成使用示例

演示如何使用Augment prompt length exceeded解决方案的各个组件。
"""

import sys
import os
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.config.augment_config import get_augment_config
from src.core.token_manager import TokenBudgetManager, ContextItem
from src.core.context_manager import ContextManager, ContextCategory
from src.core.priority_engine import PriorityEngine
from src.core.smart_truncator import SmartTruncator, TruncationStrategy
from src.core.cache.context_cache import LayeredCacheManager
from src.core.distributed_processor import DistributedProcessor
from src.core.code_compressor import CodeCompressor, CompressionLevel
from src.tools.ast_analyzer import ASTAnalyzer
from src.tools.smart_file_filter import SmartFileFilter


class AugmentIntegrationDemo:
    """Augment集成演示"""
    
    def __init__(self):
        """初始化演示"""
        print("🚀 初始化Augment系统...")
        
        # 加载配置
        try:
            self.config = get_augment_config()
            self.config_dict = self.config.dict()
            print("✅ 配置加载成功")
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            # 使用默认配置
            self.config_dict = {
                'augment': {
                    'context_management': {
                        'max_tokens': 10000,
                        'warning_threshold': 0.8,
                        'critical_threshold': 0.95
                    },
                    'optimization': {
                        'compression_enabled': True,
                        'smart_truncation': {'enabled': True}
                    },
                    'cache': {'layered_cache_enabled': True}
                }
            }
        
        # 初始化组件
        self.token_manager = TokenBudgetManager(self.config_dict)
        self.context_manager = ContextManager(self.config_dict)
        self.priority_engine = PriorityEngine(self.config_dict)
        self.smart_truncator = SmartTruncator(self.config_dict)
        self.cache_manager = LayeredCacheManager(self.config_dict)
        self.distributed_processor = DistributedProcessor(self.config_dict)
        self.code_compressor = CodeCompressor(self.config_dict)
        self.ast_analyzer = ASTAnalyzer()
        self.smart_filter = SmartFileFilter(self.config_dict)
        
        print("✅ 所有组件初始化完成")
    
    def demo_basic_workflow(self):
        """演示基础工作流程"""
        print("\n📋 演示基础工作流程")
        print("=" * 50)
        
        # 创建示例代码内容
        sample_code = '''
import os
import sys
from typing import Dict, List

class DataProcessor:
    """数据处理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.data = []
    
    def process_data(self, input_data: List[str]) -> List[str]:
        """处理数据"""
        processed = []
        for item in input_data:
            if item.strip():
                processed.append(item.upper())
        return processed
    
    def save_results(self, results: List[str], filename: str):
        """保存结果"""
        with open(filename, 'w') as f:
            for result in results:
                f.write(result + '\\n')

def main():
    """主函数"""
    processor = DataProcessor({'debug': True})
    data = ['hello', 'world', 'test']
    results = processor.process_data(data)
    processor.save_results(results, 'output.txt')

if __name__ == "__main__":
    main()
'''
        
        # 1. 创建上下文项目
        print("1. 创建上下文项目...")
        context_items = [
            ContextItem(
                content=sample_code,
                priority=0.9,
                category="core",
                file_path="data_processor.py"
            ),
            ContextItem(
                content="import logging\nlogger = logging.getLogger(__name__)",
                priority=0.5,
                category="related",
                file_path="utils.py"
            ),
            ContextItem(
                content="# Configuration file\nDEBUG = True\nLOG_LEVEL = 'INFO'",
                priority=0.3,
                category="background",
                file_path="config.py"
            )
        ]
        
        # 2. 计算重要性评分
        print("2. 计算重要性评分...")
        for item in context_items:
            item.importance_score = self.priority_engine.calculate_importance_score(item)
            print(f"   {item.file_path}: {item.importance_score:.3f}")
        
        # 3. 添加到上下文管理器
        print("3. 添加到上下文管理器...")
        for item in context_items:
            self.context_manager.add_context_item(item)
        
        # 4. 获取优化后的上下文
        print("4. 获取优化后的上下文...")
        target_tokens = 2000
        optimized_context = self.context_manager.get_optimized_context(target_tokens)
        total_tokens = sum(item.token_count for item in optimized_context)
        print(f"   优化后: {len(optimized_context)} 项目, {total_tokens} tokens")
        
        # 5. 检查token预算
        print("5. 检查token预算...")
        status, ratio = self.token_manager.check_budget_status(total_tokens)
        print(f"   状态: {status}, 使用率: {ratio:.1%}")
        
        if self.token_manager.should_trigger_warning(total_tokens):
            warning_msg = self.token_manager.get_warning_message(total_tokens)
            print(f"   ⚠️ {warning_msg}")
        
        # 6. 智能截断演示
        if total_tokens > target_tokens:
            print("6. 执行智能截断...")
            combined_content = "\n".join(item.content for item in optimized_context)
            truncated = self.smart_truncator.truncate_content(
                combined_content, target_tokens, TruncationStrategy.HYBRID
            )
            truncated_tokens = len(truncated) // 4
            print(f"   截断后: {truncated_tokens} tokens")
            
            summary = self.smart_truncator.get_truncation_summary(combined_content, truncated)
            print(f"   减少: {summary['tokens_reduction_ratio']:.1%}")
        
        print("✅ 基础工作流程演示完成")
    
    def demo_advanced_features(self):
        """演示高级功能"""
        print("\n🔬 演示高级功能")
        print("=" * 50)
        
        sample_code = '''
class AdvancedProcessor:
    def __init__(self):
        self.data = {}
    
    def complex_algorithm(self, input_data):
        result = []
        for i, item in enumerate(input_data):
            if i % 2 == 0:
                result.append(item * 2)
            else:
                result.append(item + 1)
        return result
'''
        
        # 1. AST分析
        print("1. AST代码分析...")
        analysis_result = self.ast_analyzer.analyze_code(sample_code)
        if 'structures' in analysis_result:
            print(f"   发现 {len(analysis_result['structures'])} 个代码结构")
            for structure in analysis_result['structures']:
                print(f"   - {structure.node_type.value}: {structure.name}")
        
        # 2. 代码压缩
        print("2. 代码语义压缩...")
        compression_result = self.code_compressor.compress_code(
            sample_code, CompressionLevel.MEDIUM
        )
        print(f"   压缩比: {compression_result.compression_ratio:.2f}")
        print(f"   减少: {compression_result.reduction_percentage:.1f}%")
        
        # 3. 缓存演示
        print("3. 分层缓存演示...")
        cache_key = "demo_context"
        test_data = {"content": sample_code, "analysis": analysis_result}
        
        self.cache_manager.set(cache_key, test_data, "core")
        cached_data = self.cache_manager.get(cache_key, "core")
        
        if cached_data:
            print("   ✅ 缓存读写成功")
        
        cache_stats = self.cache_manager.get_statistics()
        print(f"   缓存统计: {cache_stats}")
        
        print("✅ 高级功能演示完成")
    
    async def demo_distributed_processing(self):
        """演示分布式处理"""
        print("\n⚡ 演示分布式处理")
        print("=" * 50)
        
        # 创建大型内容
        large_content = "\n".join([
            f"def function_{i}():\n    return {i} * 2\n"
            for i in range(100)
        ])
        
        print(f"原始内容: {len(large_content)} 字符")
        
        # 定义处理函数
        def simple_processor(content: str) -> str:
            lines = content.split('\n')
            return f"Processed {len(lines)} lines"
        
        # 分布式处理
        print("执行分布式处理...")
        start_time = datetime.now()
        
        result = await self.distributed_processor.process_large_request(
            large_content, simple_processor
        )
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        print(f"处理结果: {result}")
        print(f"处理时间: {processing_time:.2f}秒")
        
        # 获取处理统计
        stats = self.distributed_processor.get_processing_stats()
        print(f"处理统计: {stats}")
        
        print("✅ 分布式处理演示完成")
    
    def demo_smart_file_filtering(self):
        """演示智能文件过滤"""
        print("\n🎯 演示智能文件过滤")
        print("=" * 50)
        
        # 模拟文件列表
        sample_files = [
            "src/core/token_manager.py",
            "src/core/context_manager.py", 
            "src/tools/ast_analyzer.py",
            "tests/test_integration.py",
            "config/settings.yaml",
            "docs/README.md",
            "examples/demo.py"
        ]
        
        print(f"文件列表: {len(sample_files)} 个文件")
        
        # 过滤相关文件
        query_context = "token management context optimization"
        relevance_scores = self.smart_filter.filter_relevant_files(
            sample_files, query_context, max_files=5
        )
        
        print("相关性评分:")
        for score in relevance_scores:
            print(f"   {score.file_path}: {score.relevance_score:.3f}")
        
        # 记录用户交互
        self.smart_filter.record_user_interaction(
            "src/core/token_manager.py", "open", query_context
        )
        
        filter_stats = self.smart_filter.get_filter_statistics()
        print(f"过滤器统计: {filter_stats}")
        
        print("✅ 智能文件过滤演示完成")
    
    def demo_performance_monitoring(self):
        """演示性能监控"""
        print("\n📊 演示性能监控")
        print("=" * 50)
        
        # Token管理统计
        print("Token管理统计:")
        token_stats = self.token_manager.get_usage_stats()
        if token_stats:
            current = token_stats.get('current', {})
            print(f"   当前使用: {current.get('total_tokens', 0)} tokens")
            print(f"   使用率: {current.get('usage_ratio', 0):.1%}")
        
        # 上下文管理统计
        print("上下文管理统计:")
        context_stats = self.context_manager.get_statistics()
        for category, stats in context_stats.items():
            if isinstance(stats, dict):
                print(f"   {category}: {stats}")
        
        # 缓存统计
        print("缓存统计:")
        cache_stats = self.cache_manager.get_statistics()
        print(f"   内存缓存: {cache_stats.get('memory_cache_size', 0)} 项目")
        print(f"   数据库总计: {cache_stats.get('database_total', 0)} 项目")
        
        print("✅ 性能监控演示完成")
    
    async def run_full_demo(self):
        """运行完整演示"""
        print("🎉 Augment系统完整演示")
        print("=" * 60)
        
        try:
            # 基础工作流程
            self.demo_basic_workflow()
            
            # 高级功能
            self.demo_advanced_features()
            
            # 分布式处理
            await self.demo_distributed_processing()
            
            # 智能文件过滤
            self.demo_smart_file_filtering()
            
            # 性能监控
            self.demo_performance_monitoring()
            
            print("\n🎊 所有演示完成！")
            print("Augment系统已准备就绪，可以有效解决prompt length exceeded问题。")
            
        except Exception as e:
            print(f"\n❌ 演示过程中出现错误: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    demo = AugmentIntegrationDemo()
    await demo.run_full_demo()


if __name__ == "__main__":
    asyncio.run(main())
