"""
用户行为学习系统

学习用户使用模式并优化上下文选择，提供个性化的Augment体验。
"""

import logging
import json
import pickle
import os
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from enum import Enum
import numpy as np


class ActionType(Enum):
    """用户行为类型"""
    FILE_OPEN = "file_open"
    FILE_EDIT = "file_edit"
    CONTEXT_REQUEST = "context_request"
    TOKEN_WARNING = "token_warning"
    TRUNCATION_APPLIED = "truncation_applied"
    CACHE_HIT = "cache_hit"
    CACHE_MISS = "cache_miss"
    SEARCH_QUERY = "search_query"
    PREFERENCE_UPDATE = "preference_update"


@dataclass
class UserAction:
    """用户行为记录"""
    action_type: ActionType
    timestamp: datetime
    context: Dict[str, Any] = field(default_factory=dict)
    file_path: Optional[str] = None
    query: Optional[str] = None
    success: bool = True
    response_time: float = 0.0
    satisfaction_score: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'action_type': self.action_type.value,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context,
            'file_path': self.file_path,
            'query': self.query,
            'success': self.success,
            'response_time': self.response_time,
            'satisfaction_score': self.satisfaction_score
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserAction':
        """从字典创建"""
        return cls(
            action_type=ActionType(data['action_type']),
            timestamp=datetime.fromisoformat(data['timestamp']),
            context=data.get('context', {}),
            file_path=data.get('file_path'),
            query=data.get('query'),
            success=data.get('success', True),
            response_time=data.get('response_time', 0.0),
            satisfaction_score=data.get('satisfaction_score')
        )


@dataclass
class UserPreference:
    """用户偏好"""
    file_types: Dict[str, float] = field(default_factory=dict)
    directories: Dict[str, float] = field(default_factory=dict)
    content_patterns: Dict[str, float] = field(default_factory=dict)
    time_patterns: Dict[str, float] = field(default_factory=dict)
    context_size_preference: float = 0.5
    compression_tolerance: float = 0.5
    response_time_expectation: float = 2.0
    
    def update_preference(self, key: str, category: str, value: float, weight: float = 0.1):
        """更新偏好"""
        if category == 'file_types':
            current = self.file_types.get(key, 0.5)
            self.file_types[key] = current * (1 - weight) + value * weight
        elif category == 'directories':
            current = self.directories.get(key, 0.5)
            self.directories[key] = current * (1 - weight) + value * weight
        elif category == 'content_patterns':
            current = self.content_patterns.get(key, 0.5)
            self.content_patterns[key] = current * (1 - weight) + value * weight


class PatternLearner:
    """模式学习器"""
    
    def __init__(self):
        """初始化学习器"""
        self.logger = logging.getLogger(__name__)
        
        # 学习参数
        self.min_pattern_frequency = 3
        self.learning_window_days = 30
        self.confidence_threshold = 0.7
    
    def learn_file_access_patterns(self, actions: List[UserAction]) -> Dict[str, float]:
        """学习文件访问模式"""
        file_access_counts = Counter()
        file_success_rates = defaultdict(list)
        
        for action in actions:
            if action.action_type in [ActionType.FILE_OPEN, ActionType.FILE_EDIT] and action.file_path:
                file_access_counts[action.file_path] += 1
                file_success_rates[action.file_path].append(1.0 if action.success else 0.0)
        
        # 计算文件重要性评分
        file_importance = {}
        total_accesses = sum(file_access_counts.values())
        
        for file_path, count in file_access_counts.items():
            if count >= self.min_pattern_frequency:
                frequency_score = count / total_accesses
                success_rate = np.mean(file_success_rates[file_path])
                importance = frequency_score * success_rate
                file_importance[file_path] = importance
        
        return file_importance
    
    def learn_time_patterns(self, actions: List[UserAction]) -> Dict[str, float]:
        """学习时间使用模式"""
        hour_activity = defaultdict(int)
        day_activity = defaultdict(int)
        
        for action in actions:
            hour = action.timestamp.hour
            day = action.timestamp.strftime('%A')
            
            hour_activity[hour] += 1
            day_activity[day] += 1
        
        # 归一化
        total_actions = len(actions)
        time_patterns = {}
        
        for hour, count in hour_activity.items():
            time_patterns[f"hour_{hour}"] = count / total_actions
        
        for day, count in day_activity.items():
            time_patterns[f"day_{day}"] = count / total_actions
        
        return time_patterns
    
    def learn_context_preferences(self, actions: List[UserAction]) -> Dict[str, float]:
        """学习上下文偏好"""
        context_preferences = {}
        
        # 分析token使用模式
        token_usages = []
        satisfaction_scores = []
        
        for action in actions:
            if action.action_type == ActionType.CONTEXT_REQUEST:
                context = action.context
                if 'token_count' in context and action.satisfaction_score is not None:
                    token_usages.append(context['token_count'])
                    satisfaction_scores.append(action.satisfaction_score)
        
        if token_usages and satisfaction_scores:
            # 找到最佳token使用量
            optimal_tokens = self._find_optimal_value(token_usages, satisfaction_scores)
            context_preferences['optimal_token_count'] = optimal_tokens
        
        # 分析压缩容忍度
        compression_actions = [a for a in actions if a.action_type == ActionType.TRUNCATION_APPLIED]
        if compression_actions:
            compression_satisfactions = [a.satisfaction_score for a in compression_actions 
                                       if a.satisfaction_score is not None]
            if compression_satisfactions:
                context_preferences['compression_tolerance'] = np.mean(compression_satisfactions)
        
        return context_preferences
    
    def _find_optimal_value(self, values: List[float], scores: List[float]) -> float:
        """找到最优值"""
        if len(values) != len(scores) or len(values) < 3:
            return np.mean(values) if values else 0.0
        
        # 使用加权平均，权重为满意度评分
        weights = np.array(scores)
        weighted_avg = np.average(values, weights=weights)
        
        return weighted_avg
    
    def detect_usage_anomalies(self, actions: List[UserAction]) -> List[Dict[str, Any]]:
        """检测使用异常"""
        anomalies = []
        
        # 检测响应时间异常
        response_times = [a.response_time for a in actions if a.response_time > 0]
        if response_times:
            mean_time = np.mean(response_times)
            std_time = np.std(response_times)
            threshold = mean_time + 2 * std_time
            
            slow_actions = [a for a in actions if a.response_time > threshold]
            if slow_actions:
                anomalies.append({
                    'type': 'slow_response',
                    'count': len(slow_actions),
                    'threshold': threshold,
                    'examples': [a.to_dict() for a in slow_actions[:3]]
                })
        
        # 检测失败率异常
        total_actions = len(actions)
        failed_actions = [a for a in actions if not a.success]
        failure_rate = len(failed_actions) / total_actions if total_actions > 0 else 0
        
        if failure_rate > 0.1:  # 失败率超过10%
            anomalies.append({
                'type': 'high_failure_rate',
                'rate': failure_rate,
                'count': len(failed_actions),
                'examples': [a.to_dict() for a in failed_actions[:3]]
            })
        
        return anomalies


class UserBehaviorTracker:
    """用户行为跟踪器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化行为跟踪器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        advanced_config = self.config.get('advanced_features', {})
        
        self.enabled = advanced_config.get('behavior_learning_enabled', False)
        self.data_file = 'data/user_behavior.json'
        self.preferences_file = 'data/user_preferences.pkl'
        
        # 行为历史
        self.action_history: List[UserAction] = []
        self.user_preferences = UserPreference()
        
        # 学习器
        self.pattern_learner = PatternLearner()
        
        # 统计信息
        self.session_start = datetime.now()
        self.total_actions = 0
        
        self.logger = logging.getLogger(__name__)
        
        # 加载历史数据
        self._load_data()
    
    def record_action(self, action_type: ActionType, **kwargs):
        """
        记录用户行为
        
        Args:
            action_type: 行为类型
            **kwargs: 其他参数
        """
        if not self.enabled:
            return
        
        action = UserAction(
            action_type=action_type,
            timestamp=datetime.now(),
            **kwargs
        )
        
        self.action_history.append(action)
        self.total_actions += 1
        
        # 定期保存数据
        if self.total_actions % 10 == 0:
            self._save_data()
        
        # 定期学习模式
        if self.total_actions % 50 == 0:
            self._update_patterns()
        
        self.logger.debug(f"Recorded action: {action_type.value}")
    
    def get_file_preference_score(self, file_path: str) -> float:
        """获取文件偏好评分"""
        if not self.enabled:
            return 0.5
        
        # 基于文件类型
        file_ext = os.path.splitext(file_path)[1].lower()
        type_score = self.user_preferences.file_types.get(file_ext, 0.5)
        
        # 基于目录
        directory = os.path.dirname(file_path)
        dir_score = self.user_preferences.directories.get(directory, 0.5)
        
        # 综合评分
        return (type_score + dir_score) / 2
    
    def get_optimal_context_size(self) -> int:
        """获取最优上下文大小"""
        if not self.enabled:
            return 5000
        
        # 基于学习到的偏好
        base_size = 5000
        preference_factor = self.user_preferences.context_size_preference
        
        return int(base_size * (0.5 + preference_factor))
    
    def should_apply_compression(self, current_tokens: int, max_tokens: int) -> bool:
        """判断是否应该应用压缩"""
        if not self.enabled:
            return current_tokens > max_tokens * 0.8
        
        usage_ratio = current_tokens / max_tokens
        tolerance = self.user_preferences.compression_tolerance
        
        # 如果用户对压缩容忍度高，更早应用压缩
        threshold = 0.8 - (tolerance - 0.5) * 0.2
        
        return usage_ratio > threshold
    
    def predict_user_satisfaction(self, context: Dict[str, Any]) -> float:
        """预测用户满意度"""
        if not self.enabled:
            return 0.7
        
        satisfaction = 0.5
        
        # 基于响应时间
        response_time = context.get('response_time', 0)
        expected_time = self.user_preferences.response_time_expectation
        
        if response_time > 0:
            time_factor = max(0, 1 - (response_time - expected_time) / expected_time)
            satisfaction += time_factor * 0.3
        
        # 基于token使用效率
        token_count = context.get('token_count', 0)
        optimal_tokens = self.user_preferences.context_size_preference * 10000
        
        if token_count > 0 and optimal_tokens > 0:
            efficiency = min(token_count / optimal_tokens, 1.0)
            satisfaction += efficiency * 0.2
        
        return min(satisfaction, 1.0)
    
    def get_personalized_recommendations(self) -> List[Dict[str, Any]]:
        """获取个性化推荐"""
        recommendations = []
        
        if not self.enabled or len(self.action_history) < 10:
            return recommendations
        
        recent_actions = self._get_recent_actions(days=7)
        
        # 分析最近的使用模式
        file_patterns = self.pattern_learner.learn_file_access_patterns(recent_actions)
        time_patterns = self.pattern_learner.learn_time_patterns(recent_actions)
        
        # 生成推荐
        if file_patterns:
            top_files = sorted(file_patterns.items(), key=lambda x: x[1], reverse=True)[:5]
            recommendations.append({
                'type': 'frequently_used_files',
                'title': '常用文件',
                'items': [{'file': f, 'score': s} for f, s in top_files]
            })
        
        # 检测异常
        anomalies = self.pattern_learner.detect_usage_anomalies(recent_actions)
        if anomalies:
            recommendations.append({
                'type': 'performance_issues',
                'title': '性能问题',
                'items': anomalies
            })
        
        return recommendations
    
    def _update_patterns(self):
        """更新学习模式"""
        if len(self.action_history) < 20:
            return
        
        recent_actions = self._get_recent_actions(days=30)
        
        # 学习文件偏好
        file_patterns = self.pattern_learner.learn_file_access_patterns(recent_actions)
        for file_path, importance in file_patterns.items():
            file_ext = os.path.splitext(file_path)[1].lower()
            directory = os.path.dirname(file_path)
            
            self.user_preferences.update_preference(file_ext, 'file_types', importance)
            self.user_preferences.update_preference(directory, 'directories', importance)
        
        # 学习上下文偏好
        context_prefs = self.pattern_learner.learn_context_preferences(recent_actions)
        if 'compression_tolerance' in context_prefs:
            self.user_preferences.compression_tolerance = context_prefs['compression_tolerance']
        
        self.logger.info("Updated user behavior patterns")
    
    def _get_recent_actions(self, days: int = 7) -> List[UserAction]:
        """获取最近的行为记录"""
        cutoff_date = datetime.now() - timedelta(days=days)
        return [action for action in self.action_history if action.timestamp > cutoff_date]
    
    def _load_data(self):
        """加载历史数据"""
        # 加载行为历史
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.action_history = [UserAction.from_dict(item) for item in data]
                self.logger.info(f"Loaded {len(self.action_history)} user actions")
            except Exception as e:
                self.logger.warning(f"Failed to load user actions: {e}")
        
        # 加载用户偏好
        if os.path.exists(self.preferences_file):
            try:
                with open(self.preferences_file, 'rb') as f:
                    self.user_preferences = pickle.load(f)
                self.logger.info("Loaded user preferences")
            except Exception as e:
                self.logger.warning(f"Failed to load user preferences: {e}")
    
    def _save_data(self):
        """保存数据"""
        # 确保目录存在
        os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
        
        # 保存行为历史（只保留最近1000条）
        try:
            recent_actions = self.action_history[-1000:]
            data = [action.to_dict() for action in recent_actions]
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save user actions: {e}")
        
        # 保存用户偏好
        try:
            with open(self.preferences_file, 'wb') as f:
                pickle.dump(self.user_preferences, f)
        except Exception as e:
            self.logger.error(f"Failed to save user preferences: {e}")
    
    def get_behavior_statistics(self) -> Dict[str, Any]:
        """获取行为统计信息"""
        recent_actions = self._get_recent_actions(days=7)
        
        stats = {
            'enabled': self.enabled,
            'total_actions': len(self.action_history),
            'recent_actions': len(recent_actions),
            'session_duration': (datetime.now() - self.session_start).total_seconds() / 3600,
            'action_types': {}
        }
        
        # 统计行为类型
        for action in recent_actions:
            action_type = action.action_type.value
            stats['action_types'][action_type] = stats['action_types'].get(action_type, 0) + 1
        
        # 平均响应时间
        response_times = [a.response_time for a in recent_actions if a.response_time > 0]
        if response_times:
            stats['avg_response_time'] = np.mean(response_times)
        
        # 成功率
        if recent_actions:
            success_rate = sum(1 for a in recent_actions if a.success) / len(recent_actions)
            stats['success_rate'] = success_rate
        
        return stats
    
    def export_data(self, file_path: str):
        """导出数据"""
        export_data = {
            'actions': [action.to_dict() for action in self.action_history],
            'preferences': asdict(self.user_preferences),
            'statistics': self.get_behavior_statistics()
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2)
        
        self.logger.info(f"Exported user behavior data to {file_path}")
    
    def __del__(self):
        """析构函数，保存数据"""
        if hasattr(self, 'action_history'):
            self._save_data()
