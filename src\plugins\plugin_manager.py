"""
插件管理器

支持第三方优化工具的集成，提供可扩展的Augment插件生态系统。
"""

import logging
import importlib
import inspect
import json
import os
from typing import Dict, List, Any, Optional, Callable, Type
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from pathlib import Path
import threading
from enum import Enum


class PluginType(Enum):
    """插件类型"""
    OPTIMIZER = "optimizer"
    ANALYZER = "analyzer"
    COMPRESSOR = "compressor"
    FILTER = "filter"
    MONITOR = "monitor"
    INTEGRATION = "integration"


@dataclass
class PluginInfo:
    """插件信息"""
    name: str
    version: str
    description: str
    author: str
    plugin_type: PluginType
    dependencies: List[str] = field(default_factory=list)
    config_schema: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'author': self.author,
            'type': self.plugin_type.value,
            'dependencies': self.dependencies,
            'config_schema': self.config_schema,
            'enabled': self.enabled
        }


class PluginInterface(ABC):
    """插件接口基类"""
    
    @abstractmethod
    def get_info(self) -> PluginInfo:
        """获取插件信息"""
        pass
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        pass
    
    @abstractmethod
    def cleanup(self):
        """清理插件资源"""
        pass
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        return True


class OptimizerPlugin(PluginInterface):
    """优化器插件基类"""
    
    @abstractmethod
    def optimize_context(self, context_items: List[Any]) -> List[Any]:
        """优化上下文"""
        pass


class AnalyzerPlugin(PluginInterface):
    """分析器插件基类"""
    
    @abstractmethod
    def analyze_code(self, code: str) -> Dict[str, Any]:
        """分析代码"""
        pass


class CompressorPlugin(PluginInterface):
    """压缩器插件基类"""
    
    @abstractmethod
    def compress(self, data: str) -> Tuple[str, float]:
        """压缩数据"""
        pass
    
    @abstractmethod
    def decompress(self, compressed_data: str) -> str:
        """解压缩数据"""
        pass


class FilterPlugin(PluginInterface):
    """过滤器插件基类"""
    
    @abstractmethod
    def filter_files(self, file_paths: List[str], context: str) -> List[str]:
        """过滤文件"""
        pass


class MonitorPlugin(PluginInterface):
    """监控器插件基类"""
    
    @abstractmethod
    def collect_metrics(self) -> Dict[str, Any]:
        """收集指标"""
        pass


class IntegrationPlugin(PluginInterface):
    """集成插件基类"""
    
    @abstractmethod
    def integrate_with_ide(self, ide_context: Dict[str, Any]) -> bool:
        """与IDE集成"""
        pass


class PluginRegistry:
    """插件注册表"""
    
    def __init__(self):
        """初始化注册表"""
        self.plugins: Dict[str, Type[PluginInterface]] = {}
        self.plugin_instances: Dict[str, PluginInterface] = {}
        self.plugin_configs: Dict[str, Dict[str, Any]] = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
    
    def register_plugin(self, plugin_class: Type[PluginInterface]) -> bool:
        """
        注册插件类
        
        Args:
            plugin_class: 插件类
            
        Returns:
            是否成功
        """
        try:
            # 创建临时实例获取信息
            temp_instance = plugin_class()
            info = temp_instance.get_info()
            
            with self.lock:
                self.plugins[info.name] = plugin_class
            
            self.logger.info(f"Registered plugin: {info.name} v{info.version}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to register plugin {plugin_class}: {e}")
            return False
    
    def unregister_plugin(self, plugin_name: str) -> bool:
        """
        注销插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            是否成功
        """
        with self.lock:
            if plugin_name in self.plugin_instances:
                try:
                    self.plugin_instances[plugin_name].cleanup()
                    del self.plugin_instances[plugin_name]
                except Exception as e:
                    self.logger.error(f"Error cleaning up plugin {plugin_name}: {e}")
            
            if plugin_name in self.plugins:
                del self.plugins[plugin_name]
                self.logger.info(f"Unregistered plugin: {plugin_name}")
                return True
        
        return False
    
    def get_plugin_instance(self, plugin_name: str) -> Optional[PluginInterface]:
        """获取插件实例"""
        with self.lock:
            if plugin_name in self.plugin_instances:
                return self.plugin_instances[plugin_name]
            
            if plugin_name in self.plugins:
                try:
                    plugin_class = self.plugins[plugin_name]
                    instance = plugin_class()
                    
                    # 初始化插件
                    config = self.plugin_configs.get(plugin_name, {})
                    if instance.initialize(config):
                        self.plugin_instances[plugin_name] = instance
                        return instance
                    
                except Exception as e:
                    self.logger.error(f"Failed to create plugin instance {plugin_name}: {e}")
        
        return None
    
    def list_plugins(self) -> List[PluginInfo]:
        """列出所有插件"""
        plugin_infos = []
        
        with self.lock:
            for plugin_name, plugin_class in self.plugins.items():
                try:
                    temp_instance = plugin_class()
                    info = temp_instance.get_info()
                    plugin_infos.append(info)
                except Exception as e:
                    self.logger.warning(f"Failed to get info for plugin {plugin_name}: {e}")
        
        return plugin_infos
    
    def set_plugin_config(self, plugin_name: str, config: Dict[str, Any]):
        """设置插件配置"""
        with self.lock:
            self.plugin_configs[plugin_name] = config


class PluginManager:
    """插件管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化插件管理器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        advanced_config = self.config.get('advanced_features', {})
        
        self.enabled = advanced_config.get('plugin_system_enabled', False)
        self.plugin_dir = Path('src/plugins/extensions')
        self.config_file = 'data/plugin_config.json'
        
        # 核心组件
        self.registry = PluginRegistry()
        
        # 插件钩子
        self.hooks: Dict[str, List[Callable]] = {}
        
        self.logger = logging.getLogger(__name__)
        
        if self.enabled:
            self._load_plugin_configs()
            self._discover_plugins()
    
    def install_plugin(self, plugin_path: str) -> bool:
        """
        安装插件
        
        Args:
            plugin_path: 插件路径
            
        Returns:
            是否成功
        """
        if not self.enabled:
            return False
        
        try:
            # 动态导入插件模块
            spec = importlib.util.spec_from_file_location("plugin", plugin_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找插件类
            plugin_classes = []
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, PluginInterface) and 
                    obj != PluginInterface):
                    plugin_classes.append(obj)
            
            if not plugin_classes:
                self.logger.error(f"No plugin class found in {plugin_path}")
                return False
            
            # 注册插件
            success = True
            for plugin_class in plugin_classes:
                if not self.registry.register_plugin(plugin_class):
                    success = False
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to install plugin from {plugin_path}: {e}")
            return False
    
    def uninstall_plugin(self, plugin_name: str) -> bool:
        """
        卸载插件
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            是否成功
        """
        if not self.enabled:
            return False
        
        return self.registry.unregister_plugin(plugin_name)
    
    def enable_plugin(self, plugin_name: str) -> bool:
        """启用插件"""
        if not self.enabled:
            return False
        
        instance = self.registry.get_plugin_instance(plugin_name)
        if instance:
            info = instance.get_info()
            info.enabled = True
            self._save_plugin_configs()
            return True
        
        return False
    
    def disable_plugin(self, plugin_name: str) -> bool:
        """禁用插件"""
        if not self.enabled:
            return False
        
        instance = self.registry.get_plugin_instance(plugin_name)
        if instance:
            info = instance.get_info()
            info.enabled = False
            self._save_plugin_configs()
            return True
        
        return False
    
    def configure_plugin(self, plugin_name: str, config: Dict[str, Any]) -> bool:
        """
        配置插件
        
        Args:
            plugin_name: 插件名称
            config: 配置字典
            
        Returns:
            是否成功
        """
        if not self.enabled:
            return False
        
        instance = self.registry.get_plugin_instance(plugin_name)
        if instance and instance.validate_config(config):
            self.registry.set_plugin_config(plugin_name, config)
            self._save_plugin_configs()
            return True
        
        return False
    
    def execute_optimizer_plugins(self, context_items: List[Any]) -> List[Any]:
        """执行优化器插件"""
        if not self.enabled:
            return context_items
        
        result = context_items
        
        for plugin_info in self.registry.list_plugins():
            if (plugin_info.plugin_type == PluginType.OPTIMIZER and 
                plugin_info.enabled):
                
                instance = self.registry.get_plugin_instance(plugin_info.name)
                if isinstance(instance, OptimizerPlugin):
                    try:
                        result = instance.optimize_context(result)
                    except Exception as e:
                        self.logger.error(f"Optimizer plugin {plugin_info.name} failed: {e}")
        
        return result
    
    def execute_analyzer_plugins(self, code: str) -> Dict[str, Any]:
        """执行分析器插件"""
        if not self.enabled:
            return {}
        
        results = {}
        
        for plugin_info in self.registry.list_plugins():
            if (plugin_info.plugin_type == PluginType.ANALYZER and 
                plugin_info.enabled):
                
                instance = self.registry.get_plugin_instance(plugin_info.name)
                if isinstance(instance, AnalyzerPlugin):
                    try:
                        analysis = instance.analyze_code(code)
                        results[plugin_info.name] = analysis
                    except Exception as e:
                        self.logger.error(f"Analyzer plugin {plugin_info.name} failed: {e}")
        
        return results
    
    def execute_filter_plugins(self, file_paths: List[str], context: str) -> List[str]:
        """执行过滤器插件"""
        if not self.enabled:
            return file_paths
        
        result = file_paths
        
        for plugin_info in self.registry.list_plugins():
            if (plugin_info.plugin_type == PluginType.FILTER and 
                plugin_info.enabled):
                
                instance = self.registry.get_plugin_instance(plugin_info.name)
                if isinstance(instance, FilterPlugin):
                    try:
                        result = instance.filter_files(result, context)
                    except Exception as e:
                        self.logger.error(f"Filter plugin {plugin_info.name} failed: {e}")
        
        return result
    
    def collect_plugin_metrics(self) -> Dict[str, Any]:
        """收集插件指标"""
        if not self.enabled:
            return {}
        
        metrics = {}
        
        for plugin_info in self.registry.list_plugins():
            if (plugin_info.plugin_type == PluginType.MONITOR and 
                plugin_info.enabled):
                
                instance = self.registry.get_plugin_instance(plugin_info.name)
                if isinstance(instance, MonitorPlugin):
                    try:
                        plugin_metrics = instance.collect_metrics()
                        metrics[plugin_info.name] = plugin_metrics
                    except Exception as e:
                        self.logger.error(f"Monitor plugin {plugin_info.name} failed: {e}")
        
        return metrics
    
    def register_hook(self, hook_name: str, callback: Callable):
        """注册钩子"""
        if hook_name not in self.hooks:
            self.hooks[hook_name] = []
        self.hooks[hook_name].append(callback)
    
    def execute_hooks(self, hook_name: str, *args, **kwargs) -> List[Any]:
        """执行钩子"""
        results = []
        
        if hook_name in self.hooks:
            for callback in self.hooks[hook_name]:
                try:
                    result = callback(*args, **kwargs)
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"Hook {hook_name} callback failed: {e}")
        
        return results
    
    def _discover_plugins(self):
        """发现插件"""
        if not self.plugin_dir.exists():
            self.plugin_dir.mkdir(parents=True, exist_ok=True)
            return
        
        for plugin_file in self.plugin_dir.glob('*.py'):
            if plugin_file.name != '__init__.py':
                self.install_plugin(str(plugin_file))
    
    def _load_plugin_configs(self):
        """加载插件配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    configs = json.load(f)
                    
                for plugin_name, config in configs.items():
                    self.registry.set_plugin_config(plugin_name, config)
                    
            except Exception as e:
                self.logger.warning(f"Failed to load plugin configs: {e}")
    
    def _save_plugin_configs(self):
        """保存插件配置"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.registry.plugin_configs, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to save plugin configs: {e}")
    
    def get_plugin_status(self) -> Dict[str, Any]:
        """获取插件状态"""
        plugin_infos = self.registry.list_plugins()
        
        return {
            'enabled': self.enabled,
            'total_plugins': len(plugin_infos),
            'enabled_plugins': len([p for p in plugin_infos if p.enabled]),
            'plugin_types': {
                ptype.value: len([p for p in plugin_infos if p.plugin_type == ptype])
                for ptype in PluginType
            },
            'plugins': [info.to_dict() for info in plugin_infos]
        }
