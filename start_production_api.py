#!/usr/bin/env python3
"""
启动生产版FastAPI服务 - 诊断版本
"""

print("=" * 60)
print("🚀 API启动诊断开始")
print("=" * 60)

# 最基础的测试
try:
    import os
    import sys
    from pathlib import Path
    print("✅ 步骤1: 基础模块导入成功")
    print(f"   Python版本: {sys.version}")
    print(f"   工作目录: {os.getcwd()}")
except Exception as e:
    print(f"❌ 步骤1: 基础模块导入失败: {e}")
    exit(1)

# 检查是否在虚拟环境中
def check_virtual_env():
    """检查是否在虚拟环境中运行"""
    print("🔍 检查虚拟环境...")
    venv_path = Path(__file__).parent / "venv"
    if venv_path.exists():
        current_python = sys.executable
        venv_python = venv_path / "Scripts" / "python.exe"

        if not str(current_python).startswith(str(venv_path)):
            print("⚠️ 检测到虚拟环境但未激活")
            print(f"🔧 推荐使用: venv\\Scripts\\activate && python {Path(__file__).name}")
            print(f"🔧 或直接使用: venv\\Scripts\\python.exe {Path(__file__).name}")
            print("🚀 继续使用当前Python环境...")
        else:
            print("✅ 虚拟环境已激活")
    else:
        print("ℹ️ 未检测到虚拟环境")

check_virtual_env()

print("📂 添加src到Python路径...")
sys.path.append('src')

print("📦 导入uvicorn...")
try:
    import uvicorn
    print("✅ uvicorn导入成功")
except Exception as e:
    print(f"❌ uvicorn导入失败: {e}")
    sys.exit(1)

print("📦 逐步导入API应用...")

# 步骤1: 测试基础导入
try:
    print("  🔍 测试基础模块导入...")
    import os
    import sys
    from pathlib import Path
    print("  ✅ 基础模块导入成功")
except Exception as e:
    print(f"  ❌ 基础模块导入失败: {e}")
    sys.exit(1)

# 步骤2: 测试pydantic导入
try:
    print("  🔍 测试pydantic导入...")
    from pydantic import BaseModel, Field
    from pydantic_settings import BaseSettings
    print("  ✅ pydantic导入成功")
except Exception as e:
    print(f"  ❌ pydantic导入失败: {e}")
    print("  💡 请安装: pip install pydantic pydantic-settings")
    sys.exit(1)

# 步骤3: 测试配置模块导入
try:
    print("  🔍 测试配置模块导入...")
    from core.config.api import APIConfig
    from core.config.cache import CacheConfig
    from core.config.database import DatabaseConfig
    from core.config.ml import MLConfig
    print("  ✅ 配置模块导入成功")
except Exception as e:
    print(f"  ❌ 配置模块导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# 步骤4: 测试Settings类导入
try:
    print("  🔍 测试Settings类导入...")
    from core.config.settings import Settings
    print("  ✅ Settings类导入成功")
except Exception as e:
    print(f"  ❌ Settings类导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# 步骤5: 测试get_settings函数
try:
    print("  🔍 测试get_settings函数...")
    from core.config import get_settings
    print("  ✅ get_settings函数导入成功")

    print("  🔍 执行get_settings()...")
    settings = get_settings()
    print("  ✅ get_settings()执行成功")
except Exception as e:
    print(f"  ❌ get_settings执行失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# 步骤6: 测试API应用导入
try:
    print("  🔍 测试API应用导入...")
    from api.production_main import app
    print("  ✅ API应用导入成功")
except Exception as e:
    print(f"  ❌ API应用导入失败: {e}")
    print("  🔍 详细错误信息:")
    import traceback
    traceback.print_exc()
    sys.exit(1)

if __name__ == "__main__":
    print("🚀 启动生产版FastAPI服务...")
    print("📍 绑定地址: 127.0.0.1:8888")
    print("📚 API文档: http://127.0.0.1:8888/docs")
    print("🔍 健康检查: http://127.0.0.1:8888/health")
    print("💡 提示: API服务将在端口8888上运行")
    print("=" * 50)

    try:
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8888,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ API服务启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
