#!/usr/bin/env python3
"""
启动生产版FastAPI服务
"""

import os
import sys
from pathlib import Path

print("🚀 开始启动API服务...")
print(f"📍 Python版本: {sys.version}")
print(f"📍 工作目录: {os.getcwd()}")

# 检查是否在虚拟环境中
def check_virtual_env():
    """检查是否在虚拟环境中运行"""
    print("🔍 检查虚拟环境...")
    venv_path = Path(__file__).parent / "venv"
    if venv_path.exists():
        current_python = sys.executable
        venv_python = venv_path / "Scripts" / "python.exe"

        if not str(current_python).startswith(str(venv_path)):
            print("⚠️ 检测到虚拟环境但未激活")
            print(f"🔧 推荐使用: venv\\Scripts\\activate && python {Path(__file__).name}")
            print(f"🔧 或直接使用: venv\\Scripts\\python.exe {Path(__file__).name}")
            print("🚀 继续使用当前Python环境...")
        else:
            print("✅ 虚拟环境已激活")
    else:
        print("ℹ️ 未检测到虚拟环境")

check_virtual_env()

print("📂 添加src到Python路径...")
sys.path.append('src')

print("📦 导入uvicorn...")
try:
    import uvicorn
    print("✅ uvicorn导入成功")
except Exception as e:
    print(f"❌ uvicorn导入失败: {e}")
    sys.exit(1)

print("📦 导入API应用...")
try:
    from api.production_main import app
    print("✅ API应用导入成功")
except Exception as e:
    print(f"❌ API应用导入失败: {e}")
    print("🔍 详细错误信息:")
    import traceback
    traceback.print_exc()
    sys.exit(1)

if __name__ == "__main__":
    print("🚀 启动生产版FastAPI服务...")
    print("📍 绑定地址: 127.0.0.1:8888")
    print("📚 API文档: http://127.0.0.1:8888/docs")
    print("🔍 健康检查: http://127.0.0.1:8888/health")
    print("💡 提示: API服务将在端口8888上运行")
    print("=" * 50)

    try:
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8888,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ API服务启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
