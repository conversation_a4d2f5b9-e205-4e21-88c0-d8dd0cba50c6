"""
Token预算管理器

实现实时token计数、预警和智能截断功能，用于解决Augment平台的prompt length exceeded问题。
"""

import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import re
import ast

try:
    import tiktoken
except ImportError:
    tiktoken = None
    logging.warning("tiktoken not available, using fallback token counting")


@dataclass
class TokenUsageStats:
    """Token使用统计"""
    total_tokens: int = 0
    core_tokens: int = 0
    related_tokens: int = 0
    background_tokens: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    response_time: float = 0.0
    truncation_applied: bool = False


@dataclass
class ContextItem:
    """上下文项目"""
    content: str
    priority: float
    category: str  # 'core', 'related', 'background'
    importance_score: float = 0.0
    token_count: int = 0


class TokenBudgetManager:
    """Token预算管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Token预算管理器
        
        Args:
            config: Augment配置字典
        """
        self.config = config.get('augment', {})
        self.context_config = self.config.get('context_management', {})
        self.budget_config = self.config.get('token_budget', {})
        
        # Token限制配置
        self.max_tokens = self.context_config.get('max_tokens', 180000)
        self.warning_threshold = self.context_config.get('warning_threshold', 0.8)
        self.critical_threshold = self.context_config.get('critical_threshold', 0.95)
        
        # 上下文分层配置
        self.layer_config = self.context_config.get('context_layers', {})
        self.core_priority = self.layer_config.get('core_priority', 0.6)
        self.related_priority = self.layer_config.get('related_priority', 0.3)
        self.background_priority = self.layer_config.get('background_priority', 0.1)
        
        # 初始化token计数器
        self.tokenizer = self._init_tokenizer()
        
        # 统计数据
        self.usage_history: List[TokenUsageStats] = []
        self.current_usage = TokenUsageStats()
        
        # 日志配置
        self.logger = logging.getLogger(__name__)
        
    def _init_tokenizer(self):
        """初始化token计数器"""
        if tiktoken:
            try:
                return tiktoken.get_encoding("cl100k_base")  # GPT-4使用的编码
            except Exception as e:
                self.logger.warning(f"Failed to initialize tiktoken: {e}")
        
        # 回退到简单的token计数
        return None
    
    def count_tokens(self, text: str) -> int:
        """
        计算文本的token数量
        
        Args:
            text: 要计算的文本
            
        Returns:
            token数量
        """
        if self.tokenizer:
            try:
                return len(self.tokenizer.encode(text))
            except Exception as e:
                self.logger.warning(f"Token counting error: {e}")
        
        # 简单的回退方法：大约4个字符=1个token
        return len(text) // 4
    
    def predict_usage(self, new_content: str) -> int:
        """
        预测添加新内容后的token使用量
        
        Args:
            new_content: 新增内容
            
        Returns:
            预测的总token数量
        """
        new_tokens = self.count_tokens(new_content)
        return self.current_usage.total_tokens + new_tokens
    
    def check_budget_status(self, token_count: int) -> Tuple[str, float]:
        """
        检查token预算状态
        
        Args:
            token_count: 当前token数量
            
        Returns:
            (状态, 使用百分比)
        """
        usage_ratio = token_count / self.max_tokens
        
        if usage_ratio >= self.critical_threshold:
            return "critical", usage_ratio
        elif usage_ratio >= self.warning_threshold:
            return "warning", usage_ratio
        else:
            return "normal", usage_ratio
    
    def smart_truncate(self, context_items: List[ContextItem], target_tokens: int) -> List[ContextItem]:
        """
        智能截断上下文，保留最重要的信息
        
        Args:
            context_items: 上下文项目列表
            target_tokens: 目标token数量
            
        Returns:
            截断后的上下文项目列表
        """
        if not context_items:
            return []
        
        # 计算每个项目的token数量
        for item in context_items:
            if item.token_count == 0:
                item.token_count = self.count_tokens(item.content)
        
        # 按重要性排序
        sorted_items = sorted(context_items, key=lambda x: x.importance_score, reverse=True)
        
        # 分层截断策略
        result = []
        current_tokens = 0
        
        # 首先保留核心上下文
        core_items = [item for item in sorted_items if item.category == 'core']
        for item in core_items:
            if current_tokens + item.token_count <= target_tokens * self.core_priority:
                result.append(item)
                current_tokens += item.token_count
        
        # 然后添加相关上下文
        related_items = [item for item in sorted_items if item.category == 'related']
        for item in related_items:
            if current_tokens + item.token_count <= target_tokens * (self.core_priority + self.related_priority):
                result.append(item)
                current_tokens += item.token_count
        
        # 最后添加背景上下文
        background_items = [item for item in sorted_items if item.category == 'background']
        for item in background_items:
            if current_tokens + item.token_count <= target_tokens:
                result.append(item)
                current_tokens += item.token_count
        
        self.logger.info(f"Smart truncation: {len(context_items)} -> {len(result)} items, "
                        f"tokens: {sum(item.token_count for item in context_items)} -> {current_tokens}")
        
        return result
    
    def calculate_importance_score(self, content: str, category: str) -> float:
        """
        计算内容的重要性评分
        
        Args:
            content: 内容文本
            category: 内容类别
            
        Returns:
            重要性评分 (0-1)
        """
        score = 0.0
        
        # 基础分数根据类别
        if category == 'core':
            score = 0.8
        elif category == 'related':
            score = 0.5
        else:  # background
            score = 0.2
        
        # 根据内容特征调整分数
        if self._contains_class_definition(content):
            score += 0.1
        if self._contains_function_definition(content):
            score += 0.1
        if self._contains_imports(content):
            score += 0.05
        if self._contains_error_handling(content):
            score += 0.05
        
        return min(score, 1.0)
    
    def _contains_class_definition(self, content: str) -> bool:
        """检查是否包含类定义"""
        return bool(re.search(r'^\s*class\s+\w+', content, re.MULTILINE))
    
    def _contains_function_definition(self, content: str) -> bool:
        """检查是否包含函数定义"""
        return bool(re.search(r'^\s*def\s+\w+', content, re.MULTILINE))
    
    def _contains_imports(self, content: str) -> bool:
        """检查是否包含导入语句"""
        return bool(re.search(r'^\s*(import|from)\s+', content, re.MULTILINE))
    
    def _contains_error_handling(self, content: str) -> bool:
        """检查是否包含错误处理"""
        return bool(re.search(r'(try:|except|finally:|raise)', content))
    
    def update_usage_stats(self, context_items: List[ContextItem], response_time: float = 0.0):
        """
        更新使用统计
        
        Args:
            context_items: 上下文项目列表
            response_time: 响应时间
        """
        stats = TokenUsageStats()
        stats.response_time = response_time
        stats.timestamp = datetime.now()
        
        for item in context_items:
            stats.total_tokens += item.token_count
            if item.category == 'core':
                stats.core_tokens += item.token_count
            elif item.category == 'related':
                stats.related_tokens += item.token_count
            else:
                stats.background_tokens += item.token_count
        
        self.current_usage = stats
        self.usage_history.append(stats)
        
        # 保持历史记录在合理范围内
        retention_days = self.budget_config.get('history_retention_days', 30)
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        self.usage_history = [s for s in self.usage_history if s.timestamp > cutoff_date]
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        获取使用统计信息
        
        Returns:
            统计信息字典
        """
        if not self.usage_history:
            return {}
        
        recent_stats = self.usage_history[-10:]  # 最近10次
        
        return {
            'current': {
                'total_tokens': self.current_usage.total_tokens,
                'core_tokens': self.current_usage.core_tokens,
                'related_tokens': self.current_usage.related_tokens,
                'background_tokens': self.current_usage.background_tokens,
                'usage_ratio': self.current_usage.total_tokens / self.max_tokens,
                'response_time': self.current_usage.response_time
            },
            'recent_average': {
                'total_tokens': sum(s.total_tokens for s in recent_stats) / len(recent_stats),
                'response_time': sum(s.response_time for s in recent_stats) / len(recent_stats)
            },
            'limits': {
                'max_tokens': self.max_tokens,
                'warning_threshold': self.warning_threshold,
                'critical_threshold': self.critical_threshold
            },
            'history_count': len(self.usage_history)
        }
    
    def should_trigger_warning(self, token_count: int) -> bool:
        """检查是否应该触发预警"""
        status, ratio = self.check_budget_status(token_count)
        return status in ['warning', 'critical']
    
    def get_warning_message(self, token_count: int) -> str:
        """获取预警消息"""
        status, ratio = self.check_budget_status(token_count)
        percentage = int(ratio * 100)
        
        if status == 'critical':
            template = self.budget_config.get('critical_message', 
                                            "Token使用量已达到{percentage}%，正在执行智能截断")
        else:
            template = self.budget_config.get('warning_message',
                                            "Token使用量已达到{percentage}%，建议优化上下文")
        
        return template.format(percentage=percentage)
