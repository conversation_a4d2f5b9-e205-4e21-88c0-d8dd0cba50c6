"""
AST代码分析器

实现代码结构分析和关键信息提取，支持多种编程语言的语法分析。
"""

import ast
import logging
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
import os


class NodeType(Enum):
    """AST节点类型"""
    CLASS = "class"
    FUNCTION = "function"
    METHOD = "method"
    IMPORT = "import"
    VARIABLE = "variable"
    CONSTANT = "constant"
    DECORATOR = "decorator"
    EXCEPTION = "exception"
    COMMENT = "comment"


@dataclass
class CodeStructure:
    """代码结构信息"""
    node_type: NodeType
    name: str
    line_start: int
    line_end: int
    complexity: int = 0
    dependencies: Set[str] = field(default_factory=set)
    docstring: Optional[str] = None
    decorators: List[str] = field(default_factory=list)
    parameters: List[str] = field(default_factory=list)
    return_type: Optional[str] = None
    parent: Optional[str] = None
    children: List['CodeStructure'] = field(default_factory=list)
    
    @property
    def line_count(self) -> int:
        """获取行数"""
        return self.line_end - self.line_start + 1
    
    @property
    def is_public(self) -> bool:
        """判断是否为公共成员"""
        return not self.name.startswith('_')
    
    @property
    def is_private(self) -> bool:
        """判断是否为私有成员"""
        return self.name.startswith('__') and not self.name.endswith('__')


class ASTAnalyzer:
    """AST代码分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = logging.getLogger(__name__)
        self.structures: List[CodeStructure] = []
        self.imports: Set[str] = set()
        self.global_variables: Set[str] = set()
        self.complexity_metrics: Dict[str, int] = {}
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """
        分析单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            分析结果字典
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self.analyze_code(content, file_path)
            
        except Exception as e:
            self.logger.error(f"Failed to analyze file {file_path}: {e}")
            return {'error': str(e)}
    
    def analyze_code(self, code: str, file_path: Optional[str] = None) -> Dict[str, Any]:
        """
        分析代码内容
        
        Args:
            code: 代码内容
            file_path: 文件路径（可选）
            
        Returns:
            分析结果字典
        """
        self.structures.clear()
        self.imports.clear()
        self.global_variables.clear()
        self.complexity_metrics.clear()
        
        try:
            # 解析AST
            tree = ast.parse(code)
            
            # 分析结构
            self._analyze_ast(tree, code.split('\n'))
            
            # 计算复杂度
            self._calculate_complexity(tree)
            
            # 提取导入信息
            self._extract_imports(tree)
            
            # 分析全局变量
            self._extract_global_variables(tree)
            
            return {
                'file_path': file_path,
                'structures': self.structures,
                'imports': list(self.imports),
                'global_variables': list(self.global_variables),
                'complexity_metrics': self.complexity_metrics,
                'summary': self._generate_summary()
            }
            
        except SyntaxError as e:
            self.logger.warning(f"Syntax error in code: {e}")
            return self._fallback_analysis(code, file_path)
        except Exception as e:
            self.logger.error(f"Analysis failed: {e}")
            return {'error': str(e)}
    
    def _analyze_ast(self, tree: ast.AST, lines: List[str]):
        """分析AST结构"""
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                self._analyze_class(node, lines)
            elif isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                self._analyze_function(node, lines)
            elif isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                self._analyze_import(node)
    
    def _analyze_class(self, node: ast.ClassDef, lines: List[str]):
        """分析类定义"""
        structure = CodeStructure(
            node_type=NodeType.CLASS,
            name=node.name,
            line_start=node.lineno,
            line_end=node.end_lineno or node.lineno,
            docstring=ast.get_docstring(node),
            decorators=[self._get_decorator_name(d) for d in node.decorator_list]
        )
        
        # 分析类的方法
        for item in node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                method_structure = self._analyze_method(item, lines, structure.name)
                structure.children.append(method_structure)
        
        self.structures.append(structure)
    
    def _analyze_function(self, node: ast.FunctionDef, lines: List[str], parent: Optional[str] = None):
        """分析函数定义"""
        node_type = NodeType.METHOD if parent else NodeType.FUNCTION
        
        structure = CodeStructure(
            node_type=node_type,
            name=node.name,
            line_start=node.lineno,
            line_end=node.end_lineno or node.lineno,
            docstring=ast.get_docstring(node),
            decorators=[self._get_decorator_name(d) for d in node.decorator_list],
            parameters=[arg.arg for arg in node.args.args],
            return_type=self._get_return_annotation(node),
            parent=parent
        )
        
        # 计算函数复杂度
        structure.complexity = self._calculate_function_complexity(node)
        
        self.structures.append(structure)
        return structure
    
    def _analyze_method(self, node: ast.FunctionDef, lines: List[str], class_name: str) -> CodeStructure:
        """分析方法定义"""
        return self._analyze_function(node, lines, class_name)
    
    def _analyze_import(self, node: ast.Import):
        """分析导入语句"""
        if isinstance(node, ast.Import):
            for alias in node.names:
                self.imports.add(alias.name)
        elif isinstance(node, ast.ImportFrom):
            module = node.module or ''
            for alias in node.names:
                if alias.name == '*':
                    self.imports.add(f"{module}.*")
                else:
                    self.imports.add(f"{module}.{alias.name}")
    
    def _calculate_complexity(self, tree: ast.AST):
        """计算整体复杂度"""
        complexity = 0
        
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                complexity += 1
            elif isinstance(node, (ast.And, ast.Or)):
                complexity += 1
            elif isinstance(node, ast.ExceptHandler):
                complexity += 1
        
        self.complexity_metrics['cyclomatic_complexity'] = complexity
        self.complexity_metrics['total_lines'] = len(tree.body) if hasattr(tree, 'body') else 0
        self.complexity_metrics['total_functions'] = len([s for s in self.structures if s.node_type == NodeType.FUNCTION])
        self.complexity_metrics['total_classes'] = len([s for s in self.structures if s.node_type == NodeType.CLASS])
    
    def _calculate_function_complexity(self, node: ast.FunctionDef) -> int:
        """计算函数复杂度"""
        complexity = 1  # 基础复杂度
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try)):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
        
        return complexity
    
    def _extract_imports(self, tree: ast.AST):
        """提取导入信息"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    self.imports.add(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ''
                for alias in node.names:
                    if alias.name == '*':
                        self.imports.add(f"{module}.*")
                    else:
                        self.imports.add(f"{module}.{alias.name}")
    
    def _extract_global_variables(self, tree: ast.AST):
        """提取全局变量"""
        for node in tree.body:
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        self.global_variables.add(target.id)
            elif isinstance(node, ast.AnnAssign) and isinstance(node.target, ast.Name):
                self.global_variables.add(node.target.id)
    
    def _get_decorator_name(self, decorator: ast.expr) -> str:
        """获取装饰器名称"""
        if isinstance(decorator, ast.Name):
            return decorator.id
        elif isinstance(decorator, ast.Attribute):
            return f"{decorator.attr}"
        elif isinstance(decorator, ast.Call):
            if isinstance(decorator.func, ast.Name):
                return decorator.func.id
            elif isinstance(decorator.func, ast.Attribute):
                return decorator.func.attr
        return "unknown"
    
    def _get_return_annotation(self, node: ast.FunctionDef) -> Optional[str]:
        """获取返回类型注解"""
        if node.returns:
            if isinstance(node.returns, ast.Name):
                return node.returns.id
            elif isinstance(node.returns, ast.Constant):
                return str(node.returns.value)
        return None
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成分析摘要"""
        return {
            'total_structures': len(self.structures),
            'classes': len([s for s in self.structures if s.node_type == NodeType.CLASS]),
            'functions': len([s for s in self.structures if s.node_type == NodeType.FUNCTION]),
            'methods': len([s for s in self.structures if s.node_type == NodeType.METHOD]),
            'imports': len(self.imports),
            'global_variables': len(self.global_variables),
            'average_complexity': sum(s.complexity for s in self.structures) / len(self.structures) if self.structures else 0,
            'max_complexity': max((s.complexity for s in self.structures), default=0),
            'public_members': len([s for s in self.structures if s.is_public]),
            'private_members': len([s for s in self.structures if s.is_private])
        }
    
    def _fallback_analysis(self, code: str, file_path: Optional[str] = None) -> Dict[str, Any]:
        """回退分析方法（当AST解析失败时）"""
        lines = code.split('\n')
        structures = []
        imports = set()
        
        # 使用正则表达式进行简单分析
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # 类定义
            class_match = re.match(r'^\s*class\s+(\w+)', line)
            if class_match:
                structures.append(CodeStructure(
                    node_type=NodeType.CLASS,
                    name=class_match.group(1),
                    line_start=i + 1,
                    line_end=i + 1  # 简化处理
                ))
            
            # 函数定义
            func_match = re.match(r'^\s*def\s+(\w+)', line)
            if func_match:
                structures.append(CodeStructure(
                    node_type=NodeType.FUNCTION,
                    name=func_match.group(1),
                    line_start=i + 1,
                    line_end=i + 1  # 简化处理
                ))
            
            # 导入语句
            import_match = re.match(r'^\s*(import|from)\s+(\w+)', line)
            if import_match:
                imports.add(import_match.group(2))
        
        return {
            'file_path': file_path,
            'structures': structures,
            'imports': list(imports),
            'global_variables': [],
            'complexity_metrics': {'fallback_analysis': True},
            'summary': {
                'total_structures': len(structures),
                'classes': len([s for s in structures if s.node_type == NodeType.CLASS]),
                'functions': len([s for s in structures if s.node_type == NodeType.FUNCTION]),
                'fallback_mode': True
            }
        }


class CodeStructureExtractor:
    """代码结构提取器"""
    
    def __init__(self):
        """初始化提取器"""
        self.analyzer = ASTAnalyzer()
        self.logger = logging.getLogger(__name__)
    
    def extract_key_structures(self, code: str, importance_threshold: float = 0.5) -> List[CodeStructure]:
        """
        提取关键代码结构
        
        Args:
            code: 代码内容
            importance_threshold: 重要性阈值
            
        Returns:
            关键结构列表
        """
        analysis_result = self.analyzer.analyze_code(code)
        
        if 'error' in analysis_result:
            return []
        
        structures = analysis_result.get('structures', [])
        key_structures = []
        
        for structure in structures:
            importance = self._calculate_importance(structure, analysis_result)
            if importance >= importance_threshold:
                key_structures.append(structure)
        
        # 按重要性排序
        key_structures.sort(key=lambda s: self._calculate_importance(s, analysis_result), reverse=True)
        
        return key_structures
    
    def _calculate_importance(self, structure: CodeStructure, analysis_result: Dict[str, Any]) -> float:
        """计算结构重要性"""
        importance = 0.0
        
        # 基础重要性
        if structure.node_type == NodeType.CLASS:
            importance += 0.8
        elif structure.node_type == NodeType.FUNCTION:
            importance += 0.6
        elif structure.node_type == NodeType.METHOD:
            importance += 0.5
        
        # 公共成员加分
        if structure.is_public:
            importance += 0.1
        
        # 有文档字符串加分
        if structure.docstring:
            importance += 0.1
        
        # 复杂度加分
        if structure.complexity > 5:
            importance += 0.2
        elif structure.complexity > 10:
            importance += 0.3
        
        # 装饰器加分
        if structure.decorators:
            importance += 0.1
        
        # 特殊方法名加分
        special_names = ['__init__', '__call__', '__enter__', '__exit__', 'main']
        if structure.name in special_names:
            importance += 0.2
        
        return min(importance, 1.0)
    
    def extract_dependencies(self, code: str) -> Dict[str, Set[str]]:
        """
        提取依赖关系
        
        Args:
            code: 代码内容
            
        Returns:
            依赖关系字典
        """
        analysis_result = self.analyzer.analyze_code(code)
        
        if 'error' in analysis_result:
            return {}
        
        dependencies = {}
        structures = analysis_result.get('structures', [])
        imports = set(analysis_result.get('imports', []))
        
        for structure in structures:
            structure_deps = set()
            
            # 添加导入依赖
            structure_deps.update(imports)
            
            # 分析结构间依赖
            for other_structure in structures:
                if other_structure.name != structure.name:
                    # 简单的名称匹配（可以进一步优化）
                    if other_structure.name in str(structure):
                        structure_deps.add(other_structure.name)
            
            dependencies[structure.name] = structure_deps
        
        return dependencies
    
    def generate_structure_summary(self, code: str) -> str:
        """
        生成结构摘要
        
        Args:
            code: 代码内容
            
        Returns:
            结构摘要字符串
        """
        analysis_result = self.analyzer.analyze_code(code)
        
        if 'error' in analysis_result:
            return f"Analysis failed: {analysis_result['error']}"
        
        summary = analysis_result.get('summary', {})
        structures = analysis_result.get('structures', [])
        
        summary_lines = [
            f"Code Structure Summary:",
            f"- Total structures: {summary.get('total_structures', 0)}",
            f"- Classes: {summary.get('classes', 0)}",
            f"- Functions: {summary.get('functions', 0)}",
            f"- Methods: {summary.get('methods', 0)}",
            f"- Imports: {summary.get('imports', 0)}",
            f"- Average complexity: {summary.get('average_complexity', 0):.2f}",
            f"- Max complexity: {summary.get('max_complexity', 0)}"
        ]
        
        # 添加关键结构信息
        key_structures = self.extract_key_structures(code, 0.7)
        if key_structures:
            summary_lines.append("\nKey Structures:")
            for structure in key_structures[:5]:  # 只显示前5个
                summary_lines.append(f"- {structure.node_type.value}: {structure.name} (complexity: {structure.complexity})")
        
        return "\n".join(summary_lines)
