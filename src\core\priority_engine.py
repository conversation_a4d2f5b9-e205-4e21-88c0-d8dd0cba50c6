"""
优先级算法引擎

实现基于多因子的重要性评分和上下文优先级算法，支持动态权重调整和用户行为模式学习。
"""

import logging
import re
import ast
import math
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import numpy as np


@dataclass
class ImportanceFactors:
    """重要性因子"""
    code_complexity: float = 0.0      # 代码复杂度
    dependency_count: int = 0          # 依赖数量
    access_frequency: float = 0.0      # 访问频率
    recency: float = 0.0              # 最近访问时间
    file_type_weight: float = 0.0     # 文件类型权重
    content_type_weight: float = 0.0  # 内容类型权重
    user_preference: float = 0.0      # 用户偏好
    semantic_relevance: float = 0.0   # 语义相关性


@dataclass
class WeightConfig:
    """权重配置"""
    code_complexity_weight: float = 0.2
    dependency_weight: float = 0.15
    access_frequency_weight: float = 0.15
    recency_weight: float = 0.1
    file_type_weight: float = 0.1
    content_type_weight: float = 0.15
    user_preference_weight: float = 0.1
    semantic_relevance_weight: float = 0.05
    
    def normalize(self):
        """归一化权重"""
        total = sum([
            self.code_complexity_weight,
            self.dependency_weight,
            self.access_frequency_weight,
            self.recency_weight,
            self.file_type_weight,
            self.content_type_weight,
            self.user_preference_weight,
            self.semantic_relevance_weight
        ])
        
        if total > 0:
            self.code_complexity_weight /= total
            self.dependency_weight /= total
            self.access_frequency_weight /= total
            self.recency_weight /= total
            self.file_type_weight /= total
            self.content_type_weight /= total
            self.user_preference_weight /= total
            self.semantic_relevance_weight /= total


class ImportanceCalculator:
    """重要性计算器"""
    
    def __init__(self):
        """初始化计算器"""
        self.logger = logging.getLogger(__name__)
        
        # 文件类型权重映射
        self.file_type_weights = {
            '.py': 1.0,
            '.js': 0.9,
            '.ts': 0.9,
            '.java': 0.8,
            '.cpp': 0.8,
            '.c': 0.8,
            '.go': 0.8,
            '.rs': 0.8,
            '.php': 0.7,
            '.rb': 0.7,
            '.yaml': 0.6,
            '.yml': 0.6,
            '.json': 0.5,
            '.xml': 0.4,
            '.md': 0.3,
            '.txt': 0.2
        }
        
        # 内容类型权重
        self.content_patterns = {
            'class_definition': (r'^\s*class\s+\w+', 1.0),
            'function_definition': (r'^\s*def\s+\w+', 0.9),
            'method_definition': (r'^\s*def\s+\w+\(self', 0.95),
            'api_endpoint': (r'@app\.(get|post|put|delete)', 0.9),
            'import_statement': (r'^\s*(import|from)\s+', 0.3),
            'error_handling': (r'(try:|except|finally:|raise)', 0.8),
            'main_function': (r'if\s+__name__\s*==\s*["\']__main__["\']', 0.9),
            'configuration': (r'(config|setting|constant)', 0.6),
            'test_code': (r'(test_|_test|assert)', 0.4),
            'comment': (r'^\s*#', 0.1),
            'docstring': (r'^\s*"""', 0.2)
        }
    
    def calculate_code_complexity(self, content: str) -> float:
        """
        计算代码复杂度
        
        Args:
            content: 代码内容
            
        Returns:
            复杂度评分 (0-1)
        """
        try:
            # 尝试解析AST
            tree = ast.parse(content)
            complexity = self._calculate_ast_complexity(tree)
            
            # 归一化到0-1范围
            return min(complexity / 20.0, 1.0)
            
        except SyntaxError:
            # 如果不是有效的Python代码，使用简单的启发式方法
            return self._calculate_heuristic_complexity(content)
    
    def _calculate_ast_complexity(self, node: ast.AST) -> int:
        """基于AST计算复杂度"""
        complexity = 0
        
        for child in ast.walk(node):
            # 控制流语句增加复杂度
            if isinstance(child, (ast.If, ast.While, ast.For, ast.Try)):
                complexity += 1
            elif isinstance(child, ast.FunctionDef):
                complexity += 1
            elif isinstance(child, ast.ClassDef):
                complexity += 2
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _calculate_heuristic_complexity(self, content: str) -> float:
        """基于启发式方法计算复杂度"""
        lines = content.split('\n')
        complexity = 0
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # 控制流关键字
            if any(keyword in line for keyword in ['if', 'elif', 'else', 'for', 'while', 'try', 'except']):
                complexity += 1
            
            # 函数和类定义
            if line.startswith('def ') or line.startswith('class '):
                complexity += 1
            
            # 嵌套层级
            indent_level = (len(line) - len(line.lstrip())) // 4
            complexity += indent_level * 0.1
        
        return min(complexity / 10.0, 1.0)
    
    def calculate_dependency_score(self, dependencies: Set[str], total_dependencies: int) -> float:
        """
        计算依赖评分
        
        Args:
            dependencies: 依赖集合
            total_dependencies: 总依赖数量
            
        Returns:
            依赖评分 (0-1)
        """
        if total_dependencies == 0:
            return 0.0
        
        dependency_count = len(dependencies)
        
        # 基础评分：依赖数量占比
        base_score = dependency_count / max(total_dependencies, 1)
        
        # 重要依赖加权
        important_deps = {'os', 'sys', 'logging', 'datetime', 'json', 'yaml', 'config'}
        important_count = len(dependencies.intersection(important_deps))
        importance_bonus = important_count * 0.1
        
        return min(base_score + importance_bonus, 1.0)
    
    def calculate_access_frequency_score(self, access_count: int, max_access: int) -> float:
        """
        计算访问频率评分
        
        Args:
            access_count: 访问次数
            max_access: 最大访问次数
            
        Returns:
            频率评分 (0-1)
        """
        if max_access == 0:
            return 0.0
        
        return min(access_count / max_access, 1.0)
    
    def calculate_recency_score(self, last_accessed: datetime) -> float:
        """
        计算最近访问评分
        
        Args:
            last_accessed: 最后访问时间
            
        Returns:
            最近性评分 (0-1)
        """
        now = datetime.now()
        time_diff = now - last_accessed
        
        # 24小时内访问得满分，之后指数衰减
        hours_diff = time_diff.total_seconds() / 3600
        
        if hours_diff <= 24:
            return 1.0
        else:
            # 指数衰减，7天后接近0
            return math.exp(-hours_diff / 168)  # 168 = 24 * 7
    
    def calculate_file_type_score(self, file_path: Optional[str]) -> float:
        """
        计算文件类型评分
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件类型评分 (0-1)
        """
        if not file_path:
            return 0.5  # 默认评分
        
        # 获取文件扩展名
        ext = '.' + file_path.split('.')[-1] if '.' in file_path else ''
        
        return self.file_type_weights.get(ext.lower(), 0.3)
    
    def calculate_content_type_score(self, content: str) -> float:
        """
        计算内容类型评分
        
        Args:
            content: 内容文本
            
        Returns:
            内容类型评分 (0-1)
        """
        max_score = 0.0
        
        for pattern_name, (pattern, weight) in self.content_patterns.items():
            if re.search(pattern, content, re.MULTILINE | re.IGNORECASE):
                max_score = max(max_score, weight)
        
        return max_score
    
    def calculate_semantic_relevance(self, content: str, query_keywords: List[str]) -> float:
        """
        计算语义相关性评分
        
        Args:
            content: 内容文本
            query_keywords: 查询关键词
            
        Returns:
            相关性评分 (0-1)
        """
        if not query_keywords:
            return 0.5  # 默认评分
        
        content_lower = content.lower()
        keyword_matches = 0
        
        for keyword in query_keywords:
            if keyword.lower() in content_lower:
                keyword_matches += 1
        
        return keyword_matches / len(query_keywords)


class PriorityEngine:
    """优先级算法引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化优先级引擎
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        self.logger = logging.getLogger(__name__)
        
        # 初始化权重配置
        self.weights = WeightConfig()
        self.weights.normalize()
        
        # 初始化计算器
        self.calculator = ImportanceCalculator()
        
        # 用户行为数据
        self.user_preferences: Dict[str, float] = {}
        self.access_history: List[Tuple[str, datetime]] = []
        self.query_history: List[str] = []
        
        # 统计数据
        self.total_dependencies = 0
        self.max_access_count = 0
    
    def calculate_importance_score(self, item: 'ContextItem', 
                                 query_keywords: Optional[List[str]] = None) -> float:
        """
        计算上下文项目的重要性评分
        
        Args:
            item: 上下文项目
            query_keywords: 查询关键词
            
        Returns:
            重要性评分 (0-1)
        """
        factors = ImportanceFactors()
        
        # 计算各个因子
        factors.code_complexity = self.calculator.calculate_code_complexity(item.content)
        factors.dependency_count = len(item.dependencies)
        factors.access_frequency = self.calculator.calculate_access_frequency_score(
            item.access_count, self.max_access_count
        )
        factors.recency = self.calculator.calculate_recency_score(item.last_accessed)
        factors.file_type_weight = self.calculator.calculate_file_type_score(item.file_path)
        factors.content_type_weight = self.calculator.calculate_content_type_score(item.content)
        factors.user_preference = self._get_user_preference_score(item)
        factors.semantic_relevance = self.calculator.calculate_semantic_relevance(
            item.content, query_keywords or []
        )
        
        # 计算加权总分
        score = (
            factors.code_complexity * self.weights.code_complexity_weight +
            (factors.dependency_count / 10.0) * self.weights.dependency_weight +
            factors.access_frequency * self.weights.access_frequency_weight +
            factors.recency * self.weights.recency_weight +
            factors.file_type_weight * self.weights.file_type_weight +
            factors.content_type_weight * self.weights.content_type_weight +
            factors.user_preference * self.weights.user_preference_weight +
            factors.semantic_relevance * self.weights.semantic_relevance_weight
        )
        
        # 确保评分在0-1范围内
        score = max(0.0, min(1.0, score))
        
        self.logger.debug(f"Importance score for {item.content[:30]}...: {score:.3f}")
        
        return score
    
    def _get_user_preference_score(self, item: 'ContextItem') -> float:
        """获取用户偏好评分"""
        if not item.file_path:
            return 0.5
        
        # 基于文件路径的偏好
        path_score = self.user_preferences.get(item.file_path, 0.5)
        
        # 基于文件类型的偏好
        if '.' in item.file_path:
            ext = '.' + item.file_path.split('.')[-1]
            type_score = self.user_preferences.get(f"type:{ext}", 0.5)
        else:
            type_score = 0.5
        
        # 基于内容模式的偏好
        content_score = 0.5
        for pattern_name, (pattern, _) in self.calculator.content_patterns.items():
            if re.search(pattern, item.content, re.MULTILINE | re.IGNORECASE):
                content_score = max(content_score, 
                                  self.user_preferences.get(f"pattern:{pattern_name}", 0.5))
        
        # 综合评分
        return (path_score + type_score + content_score) / 3.0
    
    def update_user_preferences(self, item: 'ContextItem', preference_score: float):
        """
        更新用户偏好
        
        Args:
            item: 上下文项目
            preference_score: 偏好评分 (0-1)
        """
        if item.file_path:
            # 更新文件偏好
            current_score = self.user_preferences.get(item.file_path, 0.5)
            # 使用指数移动平均
            self.user_preferences[item.file_path] = 0.8 * current_score + 0.2 * preference_score
            
            # 更新文件类型偏好
            if '.' in item.file_path:
                ext = '.' + item.file_path.split('.')[-1]
                type_key = f"type:{ext}"
                current_type_score = self.user_preferences.get(type_key, 0.5)
                self.user_preferences[type_key] = 0.9 * current_type_score + 0.1 * preference_score
    
    def learn_from_access_pattern(self, item: 'ContextItem'):
        """
        从访问模式中学习
        
        Args:
            item: 被访问的上下文项目
        """
        # 记录访问历史
        if item.file_path:
            self.access_history.append((item.file_path, datetime.now()))
            
            # 保持历史记录在合理范围内
            if len(self.access_history) > 1000:
                self.access_history = self.access_history[-500:]
        
        # 更新最大访问次数
        self.max_access_count = max(self.max_access_count, item.access_count)
        
        # 分析访问模式
        self._analyze_access_patterns()
    
    def _analyze_access_patterns(self):
        """分析访问模式"""
        if len(self.access_history) < 10:
            return
        
        # 分析最近访问的文件类型
        recent_accesses = self.access_history[-50:]
        file_types = Counter()
        
        for file_path, _ in recent_accesses:
            if '.' in file_path:
                ext = '.' + file_path.split('.')[-1]
                file_types[ext] += 1
        
        # 更新文件类型偏好
        total_accesses = len(recent_accesses)
        for file_type, count in file_types.items():
            preference = count / total_accesses
            type_key = f"type:{file_type}"
            current_pref = self.user_preferences.get(type_key, 0.5)
            self.user_preferences[type_key] = 0.7 * current_pref + 0.3 * preference
    
    def adjust_weights_based_on_feedback(self, feedback: Dict[str, float]):
        """
        基于反馈调整权重
        
        Args:
            feedback: 反馈字典，键为权重名称，值为调整量
        """
        for weight_name, adjustment in feedback.items():
            if hasattr(self.weights, weight_name):
                current_value = getattr(self.weights, weight_name)
                new_value = max(0.0, min(1.0, current_value + adjustment))
                setattr(self.weights, weight_name, new_value)
        
        # 重新归一化权重
        self.weights.normalize()
        
        self.logger.info(f"Adjusted weights based on feedback: {feedback}")
    
    def get_priority_explanation(self, item: 'ContextItem') -> Dict[str, Any]:
        """
        获取优先级评分的解释
        
        Args:
            item: 上下文项目
            
        Returns:
            解释字典
        """
        factors = ImportanceFactors()
        
        # 重新计算各个因子（用于解释）
        factors.code_complexity = self.calculator.calculate_code_complexity(item.content)
        factors.dependency_count = len(item.dependencies)
        factors.access_frequency = self.calculator.calculate_access_frequency_score(
            item.access_count, self.max_access_count
        )
        factors.recency = self.calculator.calculate_recency_score(item.last_accessed)
        factors.file_type_weight = self.calculator.calculate_file_type_score(item.file_path)
        factors.content_type_weight = self.calculator.calculate_content_type_score(item.content)
        factors.user_preference = self._get_user_preference_score(item)
        
        return {
            'factors': {
                'code_complexity': factors.code_complexity,
                'dependency_count': factors.dependency_count,
                'access_frequency': factors.access_frequency,
                'recency': factors.recency,
                'file_type_weight': factors.file_type_weight,
                'content_type_weight': factors.content_type_weight,
                'user_preference': factors.user_preference
            },
            'weights': {
                'code_complexity_weight': self.weights.code_complexity_weight,
                'dependency_weight': self.weights.dependency_weight,
                'access_frequency_weight': self.weights.access_frequency_weight,
                'recency_weight': self.weights.recency_weight,
                'file_type_weight': self.weights.file_type_weight,
                'content_type_weight': self.weights.content_type_weight,
                'user_preference_weight': self.weights.user_preference_weight
            }
        }
