"""
智能文件过滤器

使用机器学习预测文件相关性，基于依赖图分析和用户行为模式学习。
"""

import logging
import os
import re
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from collections import defaultdict, Counter
import numpy as np
from datetime import datetime, timedelta

# 尝试导入机器学习库
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.naive_bayes import MultinomialNB
    from sklearn.model_selection import train_test_split
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available, using fallback methods")


@dataclass
class FileRelevanceScore:
    """文件相关性评分"""
    file_path: str
    relevance_score: float
    confidence: float
    factors: Dict[str, float]
    reasoning: List[str]


@dataclass
class UserInteraction:
    """用户交互记录"""
    file_path: str
    action: str  # 'open', 'edit', 'reference', 'ignore'
    timestamp: datetime
    context: str = ""
    relevance_feedback: Optional[float] = None


class DependencyAnalyzer:
    """依赖关系分析器"""
    
    def __init__(self):
        """初始化依赖分析器"""
        self.logger = logging.getLogger(__name__)
        self.dependency_patterns = {
            'python': [
                r'from\s+([a-zA-Z_][a-zA-Z0-9_.]*)\s+import',
                r'import\s+([a-zA-Z_][a-zA-Z0-9_.]*)',
            ],
            'javascript': [
                r'import\s+.*\s+from\s+["\']([^"\']+)["\']',
                r'require\(["\']([^"\']+)["\']\)',
            ],
            'typescript': [
                r'import\s+.*\s+from\s+["\']([^"\']+)["\']',
                r'import\s+["\']([^"\']+)["\']',
            ]
        }
    
    def analyze_file_dependencies(self, file_path: str) -> Set[str]:
        """
        分析文件依赖关系
        
        Args:
            file_path: 文件路径
            
        Returns:
            依赖文件集合
        """
        dependencies = set()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_ext = os.path.splitext(file_path)[1].lower()
            language = self._detect_language(file_ext)
            
            if language in self.dependency_patterns:
                patterns = self.dependency_patterns[language]
                for pattern in patterns:
                    matches = re.findall(pattern, content, re.MULTILINE)
                    for match in matches:
                        # 转换为相对路径
                        dep_path = self._resolve_dependency_path(match, file_path, language)
                        if dep_path:
                            dependencies.add(dep_path)
            
        except Exception as e:
            self.logger.warning(f"Failed to analyze dependencies for {file_path}: {e}")
        
        return dependencies
    
    def build_dependency_graph(self, file_paths: List[str]) -> Dict[str, Set[str]]:
        """
        构建依赖关系图
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            依赖关系图
        """
        dependency_graph = {}
        
        for file_path in file_paths:
            dependencies = self.analyze_file_dependencies(file_path)
            dependency_graph[file_path] = dependencies
        
        return dependency_graph
    
    def _detect_language(self, file_ext: str) -> str:
        """检测编程语言"""
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.jsx': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
        }
        return language_map.get(file_ext, 'unknown')
    
    def _resolve_dependency_path(self, import_name: str, current_file: str, language: str) -> Optional[str]:
        """解析依赖路径"""
        current_dir = os.path.dirname(current_file)
        
        # 处理相对导入
        if import_name.startswith('.'):
            if language == 'python':
                # Python相对导入
                parts = import_name.split('.')
                relative_path = os.path.join(current_dir, *parts[1:])
                if os.path.exists(relative_path + '.py'):
                    return relative_path + '.py'
                elif os.path.exists(os.path.join(relative_path, '__init__.py')):
                    return os.path.join(relative_path, '__init__.py')
            else:
                # JavaScript/TypeScript相对导入
                relative_path = os.path.join(current_dir, import_name)
                for ext in ['.js', '.ts', '.jsx', '.tsx']:
                    if os.path.exists(relative_path + ext):
                        return relative_path + ext
        
        # 处理绝对导入（简化处理）
        if language == 'python':
            # 尝试在当前项目中查找
            parts = import_name.split('.')
            possible_path = os.path.join(os.getcwd(), *parts) + '.py'
            if os.path.exists(possible_path):
                return possible_path
        
        return None


class RelevancePredictor:
    """相关性预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.logger = logging.getLogger(__name__)
        self.vectorizer = TfidfVectorizer(max_features=1000) if SKLEARN_AVAILABLE else None
        self.classifier = MultinomialNB() if SKLEARN_AVAILABLE else None
        self.is_trained = False
        
        # 特征权重
        self.feature_weights = {
            'name_similarity': 0.2,
            'content_similarity': 0.3,
            'dependency_relation': 0.25,
            'directory_proximity': 0.15,
            'file_type_match': 0.1
        }
    
    def train_from_interactions(self, interactions: List[UserInteraction], 
                              file_contents: Dict[str, str]):
        """
        从用户交互中训练模型
        
        Args:
            interactions: 用户交互记录
            file_contents: 文件内容字典
        """
        if not SKLEARN_AVAILABLE or not interactions:
            return
        
        try:
            # 准备训练数据
            texts = []
            labels = []
            
            for interaction in interactions:
                if interaction.file_path in file_contents:
                    content = file_contents[interaction.file_path]
                    texts.append(content)
                    
                    # 根据用户行为确定标签
                    if interaction.relevance_feedback is not None:
                        labels.append(1 if interaction.relevance_feedback > 0.5 else 0)
                    elif interaction.action in ['open', 'edit']:
                        labels.append(1)
                    elif interaction.action == 'ignore':
                        labels.append(0)
                    else:
                        labels.append(0)  # 默认不相关
            
            if len(texts) < 5:  # 需要足够的训练数据
                return
            
            # 特征提取
            X = self.vectorizer.fit_transform(texts)
            y = np.array(labels)
            
            # 训练分类器
            if len(np.unique(y)) > 1:  # 确保有正负样本
                self.classifier.fit(X, y)
                self.is_trained = True
                self.logger.info(f"Trained relevance predictor with {len(texts)} samples")
            
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
    
    def predict_relevance(self, file_path: str, content: str, 
                         query_context: str = "") -> float:
        """
        预测文件相关性
        
        Args:
            file_path: 文件路径
            content: 文件内容
            query_context: 查询上下文
            
        Returns:
            相关性评分 (0-1)
        """
        if SKLEARN_AVAILABLE and self.is_trained and self.vectorizer and self.classifier:
            try:
                # 使用训练好的模型
                X = self.vectorizer.transform([content])
                prob = self.classifier.predict_proba(X)[0]
                return prob[1] if len(prob) > 1 else 0.5
            except Exception as e:
                self.logger.warning(f"ML prediction failed: {e}")
        
        # 回退到启发式方法
        return self._heuristic_relevance(file_path, content, query_context)
    
    def _heuristic_relevance(self, file_path: str, content: str, 
                           query_context: str = "") -> float:
        """启发式相关性计算"""
        relevance = 0.0
        
        # 文件名相似性
        if query_context:
            query_words = set(query_context.lower().split())
            file_words = set(os.path.basename(file_path).lower().split('.')[0].split('_'))
            name_similarity = len(query_words.intersection(file_words)) / max(len(query_words), 1)
            relevance += name_similarity * self.feature_weights['name_similarity']
        
        # 内容关键词匹配
        if query_context:
            content_lower = content.lower()
            keyword_matches = sum(1 for word in query_context.lower().split() 
                                if word in content_lower)
            content_similarity = min(keyword_matches / max(len(query_context.split()), 1), 1.0)
            relevance += content_similarity * self.feature_weights['content_similarity']
        
        # 文件类型重要性
        file_ext = os.path.splitext(file_path)[1].lower()
        important_extensions = {'.py': 0.9, '.js': 0.8, '.ts': 0.8, '.java': 0.7}
        type_score = important_extensions.get(file_ext, 0.5)
        relevance += type_score * self.feature_weights['file_type_match']
        
        return min(relevance, 1.0)


class SmartFileFilter:
    """智能文件过滤器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化智能文件过滤器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        advanced_config = self.config.get('advanced_features', {})
        
        self.enabled = advanced_config.get('ai_file_filtering_enabled', False)
        
        self.dependency_analyzer = DependencyAnalyzer()
        self.relevance_predictor = RelevancePredictor()
        
        # 用户交互历史
        self.interaction_history: List[UserInteraction] = []
        
        # 文件缓存
        self.file_content_cache: Dict[str, str] = {}
        self.dependency_cache: Dict[str, Set[str]] = {}
        
        self.logger = logging.getLogger(__name__)
    
    def filter_relevant_files(self, file_paths: List[str], 
                            query_context: str = "",
                            max_files: int = 20) -> List[FileRelevanceScore]:
        """
        过滤相关文件
        
        Args:
            file_paths: 文件路径列表
            query_context: 查询上下文
            max_files: 最大文件数量
            
        Returns:
            相关性评分列表
        """
        if not self.enabled:
            # 如果未启用，返回所有文件的默认评分
            return [FileRelevanceScore(
                file_path=fp,
                relevance_score=0.5,
                confidence=0.5,
                factors={'default': 0.5},
                reasoning=['AI filtering disabled']
            ) for fp in file_paths[:max_files]]
        
        relevance_scores = []
        
        # 构建依赖关系图
        dependency_graph = self._get_dependency_graph(file_paths)
        
        for file_path in file_paths:
            try:
                score = self._calculate_file_relevance(
                    file_path, query_context, dependency_graph
                )
                relevance_scores.append(score)
            except Exception as e:
                self.logger.warning(f"Failed to score {file_path}: {e}")
                relevance_scores.append(FileRelevanceScore(
                    file_path=file_path,
                    relevance_score=0.1,
                    confidence=0.1,
                    factors={'error': 0.1},
                    reasoning=[f'Error: {str(e)}']
                ))
        
        # 按相关性排序
        relevance_scores.sort(key=lambda x: x.relevance_score, reverse=True)
        
        return relevance_scores[:max_files]
    
    def _calculate_file_relevance(self, file_path: str, query_context: str,
                                dependency_graph: Dict[str, Set[str]]) -> FileRelevanceScore:
        """计算文件相关性"""
        factors = {}
        reasoning = []
        
        # 获取文件内容
        content = self._get_file_content(file_path)
        
        # 1. ML预测相关性
        ml_score = self.relevance_predictor.predict_relevance(file_path, content, query_context)
        factors['ml_prediction'] = ml_score
        
        # 2. 依赖关系评分
        dependency_score = self._calculate_dependency_score(file_path, dependency_graph)
        factors['dependency_relation'] = dependency_score
        
        # 3. 文件名相似性
        name_score = self._calculate_name_similarity(file_path, query_context)
        factors['name_similarity'] = name_score
        
        # 4. 目录接近度
        directory_score = self._calculate_directory_proximity(file_path, query_context)
        factors['directory_proximity'] = directory_score
        
        # 5. 文件类型匹配
        type_score = self._calculate_file_type_score(file_path)
        factors['file_type_match'] = type_score
        
        # 6. 用户历史偏好
        user_score = self._calculate_user_preference_score(file_path)
        factors['user_preference'] = user_score
        
        # 综合评分
        weights = {
            'ml_prediction': 0.3,
            'dependency_relation': 0.25,
            'name_similarity': 0.15,
            'directory_proximity': 0.1,
            'file_type_match': 0.1,
            'user_preference': 0.1
        }
        
        final_score = sum(factors[key] * weights.get(key, 0) for key in factors)
        
        # 生成推理说明
        if final_score > 0.7:
            reasoning.append("High relevance: strong ML prediction and dependencies")
        elif final_score > 0.5:
            reasoning.append("Medium relevance: some matching factors")
        else:
            reasoning.append("Low relevance: few matching factors")
        
        # 计算置信度
        confidence = min(max(final_score, 0.1), 0.9)
        
        return FileRelevanceScore(
            file_path=file_path,
            relevance_score=final_score,
            confidence=confidence,
            factors=factors,
            reasoning=reasoning
        )
    
    def _get_dependency_graph(self, file_paths: List[str]) -> Dict[str, Set[str]]:
        """获取依赖关系图（使用缓存）"""
        cache_key = hash(tuple(sorted(file_paths)))
        
        if cache_key not in self.dependency_cache:
            self.dependency_cache[cache_key] = self.dependency_analyzer.build_dependency_graph(file_paths)
        
        return self.dependency_cache[cache_key]
    
    def _get_file_content(self, file_path: str) -> str:
        """获取文件内容（使用缓存）"""
        if file_path not in self.file_content_cache:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.file_content_cache[file_path] = f.read()
            except Exception as e:
                self.logger.warning(f"Failed to read {file_path}: {e}")
                self.file_content_cache[file_path] = ""
        
        return self.file_content_cache[file_path]
    
    def _calculate_dependency_score(self, file_path: str, 
                                  dependency_graph: Dict[str, Set[str]]) -> float:
        """计算依赖关系评分"""
        dependencies = dependency_graph.get(file_path, set())
        
        # 被依赖的文件更重要
        dependents = sum(1 for deps in dependency_graph.values() if file_path in deps)
        
        # 综合评分
        dep_score = min(len(dependencies) * 0.1, 0.5)  # 依赖数量
        dependent_score = min(dependents * 0.2, 0.5)   # 被依赖数量
        
        return dep_score + dependent_score
    
    def _calculate_name_similarity(self, file_path: str, query_context: str) -> float:
        """计算文件名相似性"""
        if not query_context:
            return 0.5
        
        file_name = os.path.basename(file_path).lower()
        query_words = set(query_context.lower().split())
        
        # 检查文件名中是否包含查询词
        matches = sum(1 for word in query_words if word in file_name)
        return min(matches / len(query_words), 1.0) if query_words else 0.0
    
    def _calculate_directory_proximity(self, file_path: str, query_context: str) -> float:
        """计算目录接近度"""
        # 简化实现：根据目录深度和名称
        dir_path = os.path.dirname(file_path).lower()
        
        # 核心目录权重更高
        core_dirs = ['src', 'core', 'main', 'lib']
        if any(core_dir in dir_path for core_dir in core_dirs):
            return 0.8
        
        # 测试目录权重较低
        if 'test' in dir_path:
            return 0.3
        
        return 0.5
    
    def _calculate_file_type_score(self, file_path: str) -> float:
        """计算文件类型评分"""
        file_ext = os.path.splitext(file_path)[1].lower()
        
        type_scores = {
            '.py': 0.9,
            '.js': 0.8,
            '.ts': 0.8,
            '.java': 0.7,
            '.cpp': 0.7,
            '.c': 0.7,
            '.yaml': 0.6,
            '.yml': 0.6,
            '.json': 0.5,
            '.md': 0.3,
            '.txt': 0.2
        }
        
        return type_scores.get(file_ext, 0.4)
    
    def _calculate_user_preference_score(self, file_path: str) -> float:
        """计算用户偏好评分"""
        # 基于历史交互计算偏好
        recent_interactions = [
            interaction for interaction in self.interaction_history
            if interaction.timestamp > datetime.now() - timedelta(days=30)
        ]
        
        file_interactions = [
            interaction for interaction in recent_interactions
            if interaction.file_path == file_path
        ]
        
        if not file_interactions:
            return 0.5
        
        # 计算平均偏好
        positive_actions = sum(1 for i in file_interactions if i.action in ['open', 'edit'])
        total_actions = len(file_interactions)
        
        return positive_actions / total_actions if total_actions > 0 else 0.5
    
    def record_user_interaction(self, file_path: str, action: str, 
                              context: str = "", relevance_feedback: Optional[float] = None):
        """记录用户交互"""
        interaction = UserInteraction(
            file_path=file_path,
            action=action,
            timestamp=datetime.now(),
            context=context,
            relevance_feedback=relevance_feedback
        )
        
        self.interaction_history.append(interaction)
        
        # 保持历史记录在合理范围内
        if len(self.interaction_history) > 1000:
            self.interaction_history = self.interaction_history[-500:]
        
        # 定期重新训练模型
        if len(self.interaction_history) % 50 == 0:
            self._retrain_model()
    
    def _retrain_model(self):
        """重新训练模型"""
        if not SKLEARN_AVAILABLE:
            return
        
        try:
            # 收集文件内容
            file_contents = {}
            for interaction in self.interaction_history[-200:]:  # 使用最近的交互
                if interaction.file_path not in file_contents:
                    file_contents[interaction.file_path] = self._get_file_content(interaction.file_path)
            
            # 重新训练
            self.relevance_predictor.train_from_interactions(
                self.interaction_history[-200:], file_contents
            )
            
        except Exception as e:
            self.logger.error(f"Model retraining failed: {e}")
    
    def get_filter_statistics(self) -> Dict[str, Any]:
        """获取过滤器统计信息"""
        return {
            'enabled': self.enabled,
            'interaction_count': len(self.interaction_history),
            'cache_size': len(self.file_content_cache),
            'model_trained': self.relevance_predictor.is_trained if SKLEARN_AVAILABLE else False,
            'sklearn_available': SKLEARN_AVAILABLE
        }
