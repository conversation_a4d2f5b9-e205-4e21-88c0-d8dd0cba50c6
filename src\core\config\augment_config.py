"""
Augment配置管理器

扩展现有配置系统，支持Augment特定配置的加载和验证。
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path
from pydantic import BaseSettings, Field, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings


class ContextLayersConfig(BaseSettings):
    """上下文分层配置"""
    core_priority: float = Field(default=0.6, ge=0.0, le=1.0, description="核心上下文优先级")
    related_priority: float = Field(default=0.3, ge=0.0, le=1.0, description="相关上下文优先级")
    background_priority: float = Field(default=0.1, ge=0.0, le=1.0, description="背景上下文优先级")
    
    @validator('*')
    def validate_priorities(cls, v, values):
        """验证优先级总和不超过1.0"""
        if len(values) == 2:  # 当所有字段都设置后
            total = sum(values.values()) + v
            if total > 1.0:
                raise ValueError(f"优先级总和不能超过1.0，当前总和: {total}")
        return v


class DynamicAdjustmentConfig(BaseSettings):
    """动态调整配置"""
    enabled: bool = Field(default=True, description="是否启用动态调整")
    adjustment_factor: float = Field(default=0.1, ge=0.0, le=0.5, description="调整幅度")
    min_core_ratio: float = Field(default=0.4, ge=0.0, le=1.0, description="核心上下文最小比例")
    max_core_ratio: float = Field(default=0.8, ge=0.0, le=1.0, description="核心上下文最大比例")


class ContextManagementConfig(BaseSettings):
    """上下文管理配置"""
    max_tokens: int = Field(default=180000, gt=0, description="最大token数量")
    warning_threshold: float = Field(default=0.8, ge=0.0, le=1.0, description="预警阈值")
    critical_threshold: float = Field(default=0.95, ge=0.0, le=1.0, description="紧急阈值")
    context_layers: ContextLayersConfig = Field(default_factory=ContextLayersConfig)
    dynamic_adjustment: DynamicAdjustmentConfig = Field(default_factory=DynamicAdjustmentConfig)
    
    @validator('critical_threshold')
    def validate_thresholds(cls, v, values):
        """验证紧急阈值大于预警阈值"""
        if 'warning_threshold' in values and v <= values['warning_threshold']:
            raise ValueError("紧急阈值必须大于预警阈值")
        return v


class SmartTruncationConfig(BaseSettings):
    """智能截断配置"""
    enabled: bool = Field(default=True, description="是否启用智能截断")
    preserve_imports: bool = Field(default=True, description="保留导入语句")
    preserve_classes: bool = Field(default=True, description="保留类定义")
    preserve_functions: bool = Field(default=True, description="保留函数定义")
    preserve_comments: bool = Field(default=False, description="保留注释")


class OptimizationConfig(BaseSettings):
    """优化策略配置"""
    auto_adjustment: bool = Field(default=True, description="启用自动调整")
    project_size_detection: bool = Field(default=True, description="启用项目规模检测")
    compression_enabled: bool = Field(default=False, description="启用压缩")
    compression_ratio: float = Field(default=0.7, ge=0.1, le=0.9, description="压缩比例")
    smart_truncation: SmartTruncationConfig = Field(default_factory=SmartTruncationConfig)


class TokenBudgetConfig(BaseSettings):
    """Token预算配置"""
    warning_enabled: bool = Field(default=True, description="启用预警")
    warning_message: str = Field(default="Token使用量已达到{percentage}%，建议优化上下文", description="预警消息")
    critical_message: str = Field(default="Token使用量已达到{percentage}%，正在执行智能截断", description="紧急消息")
    statistics_enabled: bool = Field(default=True, description="启用统计")
    history_retention_days: int = Field(default=30, gt=0, description="历史数据保留天数")
    performance_tracking: bool = Field(default=True, description="启用性能跟踪")
    response_time_threshold: float = Field(default=2.0, gt=0, description="响应时间阈值")


class CacheConfig(BaseSettings):
    """缓存配置"""
    layered_cache_enabled: bool = Field(default=True, description="启用分层缓存")
    cache_ttl: int = Field(default=3600, gt=0, description="缓存生存时间")
    cache_strategy: str = Field(default="lru", description="缓存策略")
    max_cache_size: int = Field(default=1000, gt=0, description="最大缓存条目数")
    persistent_cache: bool = Field(default=True, description="启用持久化缓存")
    cache_file: str = Field(default="data/augment_cache.db", description="缓存文件路径")


class MonitoringConfig(BaseSettings):
    """监控配置"""
    real_time_monitoring: bool = Field(default=True, description="启用实时监控")
    metrics_collection_interval: int = Field(default=60, gt=0, description="指标收集间隔")
    dashboard_enabled: bool = Field(default=True, description="启用监控仪表板")
    dashboard_refresh_interval: int = Field(default=30, gt=0, description="仪表板刷新间隔")
    logging_enabled: bool = Field(default=True, description="启用日志记录")
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="logs/augment.log", description="日志文件路径")


class AdvancedFeaturesConfig(BaseSettings):
    """高级功能配置"""
    ast_analysis_enabled: bool = Field(default=False, description="AST分析")
    semantic_compression_enabled: bool = Field(default=False, description="语义压缩")
    behavior_learning_enabled: bool = Field(default=False, description="用户行为学习")
    distributed_processing_enabled: bool = Field(default=False, description="分布式处理")
    ai_file_filtering_enabled: bool = Field(default=False, description="AI文件过滤")
    plugin_system_enabled: bool = Field(default=False, description="插件系统")


class AugmentConfig(PydanticBaseSettings):
    """Augment主配置类"""
    context_management: ContextManagementConfig = Field(default_factory=ContextManagementConfig)
    optimization: OptimizationConfig = Field(default_factory=OptimizationConfig)
    token_budget: TokenBudgetConfig = Field(default_factory=TokenBudgetConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    advanced_features: AdvancedFeaturesConfig = Field(default_factory=AdvancedFeaturesConfig)
    
    class Config:
        env_prefix = "AUGMENT_"
        env_file = ".env"
        case_sensitive = False


class AugmentConfigLoader:
    """Augment配置加载器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置加载器
        
        Args:
            config_path: 配置文件路径，默认为config/augment.yaml
        """
        self.config_path = config_path or "config/augment.yaml"
        self.environment = os.getenv("ENVIRONMENT", "development")
        self._config_cache: Optional[Dict[str, Any]] = None
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        if self._config_cache is not None:
            return self._config_cache
        
        config_file = Path(self.config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 合并环境特定配置
            base_config = config_data.get('augment', {})
            env_config = config_data.get(self.environment, {}).get('augment', {})
            
            # 深度合并配置
            merged_config = self._deep_merge(base_config, env_config)
            
            self._config_cache = {'augment': merged_config}
            return self._config_cache
            
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def get_augment_config(self) -> AugmentConfig:
        """
        获取验证后的Augment配置对象
        
        Returns:
            AugmentConfig实例
        """
        config_data = self.load_config()
        augment_data = config_data.get('augment', {})
        
        try:
            return AugmentConfig(**augment_data)
        except Exception as e:
            raise ValueError(f"配置验证失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        self._config_cache = None
        return self.load_config()
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并两个字典
        
        Args:
            base: 基础字典
            override: 覆盖字典
            
        Returns:
            合并后的字典
        """
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def validate_config(self, config_data: Dict[str, Any]) -> List[str]:
        """
        验证配置数据
        
        Args:
            config_data: 配置数据
            
        Returns:
            验证错误列表
        """
        errors = []
        
        try:
            AugmentConfig(**config_data.get('augment', {}))
        except Exception as e:
            errors.append(str(e))
        
        return errors


# 全局配置加载器实例
_config_loader: Optional[AugmentConfigLoader] = None


def get_augment_config() -> AugmentConfig:
    """
    获取全局Augment配置实例
    
    Returns:
        AugmentConfig实例
    """
    global _config_loader
    
    if _config_loader is None:
        _config_loader = AugmentConfigLoader()
    
    return _config_loader.get_augment_config()


def reload_augment_config():
    """重新加载Augment配置"""
    global _config_loader
    
    if _config_loader is not None:
        _config_loader.reload_config()
