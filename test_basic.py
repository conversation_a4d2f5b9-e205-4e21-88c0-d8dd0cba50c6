#!/usr/bin/env python3
"""
最基础的Python测试脚本
"""

print("开始基础测试...")

try:
    print("测试1: 基础导入")
    import os
    import sys
    print("✅ 基础导入成功")
except Exception as e:
    print(f"❌ 基础导入失败: {e}")
    exit(1)

try:
    print("测试2: 路径操作")
    from pathlib import Path
    current_dir = Path.cwd()
    print(f"✅ 当前目录: {current_dir}")
except Exception as e:
    print(f"❌ 路径操作失败: {e}")
    exit(1)

try:
    print("测试3: FastAPI导入")
    from fastapi import FastAPI
    print("✅ FastAPI导入成功")
except Exception as e:
    print(f"❌ FastAPI导入失败: {e}")
    exit(1)

try:
    print("测试4: uvicorn导入")
    import uvicorn
    print("✅ uvicorn导入成功")
except Exception as e:
    print(f"❌ uvicorn导入失败: {e}")
    exit(1)

print("🎉 所有基础测试通过！")
print("Python环境正常，可以继续调试API启动问题。")
