# Augment Prompt Length Exceeded 解决方案

## 🎯 项目概述

本项目为Augment平台实现了一套完整的prompt length exceeded错误解决方案，通过智能配置优化、分层上下文管理、token预算管理等多个技术方案，从根本上解决token限制问题。

## ✨ 核心功能

### 1. 智能配置管理
- **配置文件**: `config/augment.yaml`
- **功能**: 支持环境特定配置、动态参数调整
- **特点**: 完整的配置验证和热重载机制

### 2. Token预算管理
- **核心组件**: `src/core/token_manager.py`
- **功能**: 实时token计数、智能预警、自动截断
- **特点**: 基于tiktoken的精确计算，支持多阈值预警

### 3. 分层上下文管理
- **核心组件**: `src/core/context_manager.py`
- **功能**: 三层上下文架构（核心/相关/背景）
- **特点**: 动态优先级调整、依赖关系分析

### 4. 优先级算法引擎
- **核心组件**: `src/core/priority_engine.py`
- **功能**: 多因子重要性评分、用户行为学习
- **特点**: 支持8种评分因子，自适应权重调整

### 5. 智能截断算法
- **核心组件**: `src/core/smart_truncator.py`
- **功能**: 结构感知截断、保留关键信息
- **特点**: 支持3种截断策略，可配置保留规则

### 6. 分层缓存系统
- **核心组件**: `src/core/cache/context_cache.py`
- **功能**: 内存+数据库双层缓存、分类存储
- **特点**: 自动过期清理、性能优化

### 7. 监控仪表板
- **界面组件**: `src/streamlit_app/pages/augment_monitor.py`
- **功能**: 实时监控、配置调整、性能分析
- **特点**: 交互式图表、智能截断演示

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install tiktoken pydantic pydantic-settings streamlit plotly pandas numpy scikit-learn
```

### 2. 配置系统
```python
from src.core.config.augment_config import get_augment_config

# 加载配置
config = get_augment_config()
```

### 3. 初始化组件
```python
from src.core.token_manager import TokenBudgetManager
from src.core.context_manager import ContextManager

# 初始化Token管理器
token_manager = TokenBudgetManager(config_data)

# 初始化上下文管理器
context_manager = ContextManager(config_data)
```

### 4. 使用示例
```python
from src.core.context_manager import ContextItem

# 创建上下文项目
item = ContextItem(
    content="def hello_world(): print('Hello!')",
    priority=0.8,
    category="core",
    file_path="main.py"
)

# 添加到管理器
context_manager.add_context_item(item)

# 获取优化后的上下文
optimized = context_manager.get_optimized_context(target_tokens=5000)

# 检查token使用情况
total_tokens = sum(item.token_count for item in optimized)
status, ratio = token_manager.check_budget_status(total_tokens)
```

## 📊 监控界面

启动Streamlit监控界面：
```bash
streamlit run src/streamlit_app/pages/augment_monitor.py
```

监控界面提供：
- 实时token使用情况
- 上下文分层分析
- 性能指标监控
- 配置参数调整
- 智能截断演示

## ⚙️ 配置说明

### 基础配置 (`config/augment.yaml`)
```yaml
augment:
  context_management:
    max_tokens: 180000          # 最大token数量
    warning_threshold: 0.8      # 预警阈值
    critical_threshold: 0.95    # 紧急阈值
    
  optimization:
    auto_adjustment: true       # 启用自动调整
    compression_enabled: false  # 压缩功能（后续版本）
    
  cache:
    layered_cache_enabled: true # 启用分层缓存
    cache_ttl: 3600            # 缓存生存时间
```

### 环境特定配置
- **开发环境**: 较小token限制、详细日志
- **生产环境**: 更大token限制、警告级别日志

## 🧪 测试

运行集成测试：
```bash
python -m pytest tests/test_augment_integration.py -v
```

测试覆盖：
- Token预算管理
- 上下文分层管理
- 优先级算法
- 智能截断
- 缓存系统
- 端到端工作流程

## 📈 性能指标

### 预期效果
- **错误减少**: 90%以上的prompt length exceeded错误
- **响应时间**: 不超过原来的120%
- **内存使用**: 优化后减少30%
- **缓存命中率**: 70%以上

### 监控指标
- Token使用率趋势
- 上下文分层分布
- 截断效果统计
- 缓存性能指标

## 🔧 高级功能

### 1. 用户行为学习
```python
# 更新用户偏好
priority_engine.update_user_preferences(item, preference_score=0.8)

# 学习访问模式
priority_engine.learn_from_access_pattern(item)
```

### 2. 智能截断策略
```python
from src.core.smart_truncator import TruncationStrategy

# 不同截断策略
strategies = [
    TruncationStrategy.IMPORTANCE_BASED,  # 基于重要性
    TruncationStrategy.STRUCTURE_AWARE,   # 结构感知
    TruncationStrategy.HYBRID            # 混合策略
]
```

### 3. 缓存优化
```python
# 手动优化缓存
cache_manager.optimize_cache()

# 清理过期条目
cache_manager.clear_expired()

# 获取缓存统计
stats = cache_manager.get_statistics()
```

## 🛠️ 故障排除

### 常见问题

1. **配置文件未找到**
   ```
   FileNotFoundError: 配置文件不存在: config/augment.yaml
   ```
   解决：确保配置文件存在且路径正确

2. **tiktoken导入失败**
   ```
   ImportError: No module named 'tiktoken'
   ```
   解决：`pip install tiktoken`

3. **数据库权限错误**
   ```
   PermissionError: [Errno 13] Permission denied: 'data/augment_cache.db'
   ```
   解决：确保data目录存在且有写权限

### 调试模式
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🔮 未来规划

### 阶段三：中期创新（2-3个月）
- AST代码分析器
- 语义压缩引擎
- 用户行为学习系统
- 性能监控扩展

### 阶段四：长期架构（3-6个月）
- 分布式任务处理
- AI驱动文件过滤
- 本地缓存优化
- 插件生态系统

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 智能配置管理
- ✅ Token预算管理
- ✅ 分层上下文管理
- ✅ 优先级算法引擎
- ✅ 智能截断算法
- ✅ 分层缓存系统
- ✅ 监控仪表板

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如有问题或建议，请：
1. 查看文档和FAQ
2. 搜索已有Issues
3. 创建新Issue描述问题
4. 联系开发团队

---

**Augment Prompt Length Exceeded 解决方案** - 让AI开发更高效！
