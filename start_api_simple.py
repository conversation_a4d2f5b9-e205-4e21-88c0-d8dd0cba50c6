#!/usr/bin/env python3
"""
简化版API启动脚本 - 快速恢复服务

这个脚本移除了所有复杂的诊断和初始化代码，
专注于快速启动基础API服务。
"""

import sys
import os
from pathlib import Path

print("🚀 启动简化版API服务...")

# 添加src到Python路径
sys.path.append('src')

try:
    import uvicorn
    print("✅ uvicorn导入成功")
except ImportError as e:
    print(f"❌ uvicorn导入失败: {e}")
    print("💡 请安装: pip install uvicorn")
    sys.exit(1)

try:
    from fastapi import FastAPI
    print("✅ FastAPI导入成功")
except ImportError as e:
    print(f"❌ FastAPI导入失败: {e}")
    print("💡 请安装: pip install fastapi")
    sys.exit(1)

# 创建最简化的FastAPI应用
app = FastAPI(
    title="福彩3D预测API - 简化版",
    description="快速恢复的API服务",
    version="1.0.0-simple"
)

@app.get("/")
def root():
    """根路径"""
    return {"message": "福彩3D预测API - 简化版运行中", "status": "ok"}

@app.get("/health")
def health():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "福彩3D预测API",
        "version": "1.0.0-simple",
        "mode": "simplified"
    }

@app.get("/api/test")
def test_endpoint():
    """测试端点"""
    return {
        "message": "API测试成功",
        "timestamp": "2025-01-14",
        "endpoints": [
            "/",
            "/health", 
            "/api/test",
            "/docs"
        ]
    }

if __name__ == "__main__":
    print("=" * 50)
    print("🎯 启动简化版FastAPI服务")
    print("📍 绑定地址: 127.0.0.1:8888")
    print("🔍 健康检查: http://127.0.0.1:8888/health")
    print("📚 API文档: http://127.0.0.1:8888/docs")
    print("🧪 测试端点: http://127.0.0.1:8888/api/test")
    print("=" * 50)
    
    try:
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8888,
            reload=False,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
