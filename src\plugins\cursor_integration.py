"""
Cursor IDE集成插件

提供与Cursor IDE的深度集成，支持实时优化和智能提示。
"""

import logging
import json
import os
import subprocess
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import threading
import time
from pathlib import Path

from .plugin_manager import IntegrationPlugin, PluginInfo, PluginType


@dataclass
class CursorContext:
    """Cursor上下文信息"""
    workspace_path: str
    active_file: Optional[str] = None
    selected_text: Optional[str] = None
    cursor_position: Optional[Dict[str, int]] = None
    open_files: List[str] = None
    project_type: Optional[str] = None
    
    def __post_init__(self):
        if self.open_files is None:
            self.open_files = []


class CursorIntegration(IntegrationPlugin):
    """Cursor IDE集成插件"""
    
    def __init__(self):
        """初始化Cursor集成"""
        self.logger = logging.getLogger(__name__)
        self.workspace_path: Optional[str] = None
        self.monitoring_thread: Optional[threading.Thread] = None
        self.monitoring_active = False
        
        # 集成状态
        self.is_connected = False
        self.last_context: Optional[CursorContext] = None
        
        # 配置
        self.auto_optimize = True
        self.real_time_suggestions = True
        self.context_update_interval = 5.0  # 秒
    
    def get_info(self) -> PluginInfo:
        """获取插件信息"""
        return PluginInfo(
            name="cursor_integration",
            version="1.0.0",
            description="Cursor IDE深度集成插件，提供实时优化和智能提示",
            author="Augment Team",
            plugin_type=PluginType.INTEGRATION,
            dependencies=["psutil"],
            config_schema={
                "auto_optimize": {"type": "boolean", "default": True},
                "real_time_suggestions": {"type": "boolean", "default": True},
                "context_update_interval": {"type": "number", "default": 5.0}
            }
        )
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        try:
            # 应用配置
            self.auto_optimize = config.get('auto_optimize', True)
            self.real_time_suggestions = config.get('real_time_suggestions', True)
            self.context_update_interval = config.get('context_update_interval', 5.0)
            
            # 检测Cursor IDE
            if self._detect_cursor_ide():
                self.is_connected = True
                
                # 启动监控线程
                if self.real_time_suggestions:
                    self._start_monitoring()
                
                self.logger.info("Cursor IDE integration initialized successfully")
                return True
            else:
                self.logger.warning("Cursor IDE not detected")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Cursor integration: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        self.monitoring_active = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5.0)
        
        self.is_connected = False
        self.logger.info("Cursor integration cleaned up")
    
    def integrate_with_ide(self, ide_context: Dict[str, Any]) -> bool:
        """与IDE集成"""
        if not self.is_connected:
            return False
        
        try:
            # 更新工作区路径
            if 'workspace_path' in ide_context:
                self.workspace_path = ide_context['workspace_path']
            
            # 创建Cursor上下文
            cursor_context = CursorContext(
                workspace_path=self.workspace_path or os.getcwd(),
                active_file=ide_context.get('active_file'),
                selected_text=ide_context.get('selected_text'),
                cursor_position=ide_context.get('cursor_position'),
                open_files=ide_context.get('open_files', []),
                project_type=ide_context.get('project_type')
            )
            
            self.last_context = cursor_context
            
            # 如果启用自动优化，触发优化
            if self.auto_optimize:
                self._trigger_optimization(cursor_context)
            
            return True
            
        except Exception as e:
            self.logger.error(f"IDE integration failed: {e}")
            return False
    
    def get_current_context(self) -> Optional[CursorContext]:
        """获取当前Cursor上下文"""
        if not self.is_connected:
            return None
        
        try:
            # 尝试从Cursor获取当前上下文
            context = self._fetch_cursor_context()
            if context:
                self.last_context = context
            
            return self.last_context
            
        except Exception as e:
            self.logger.error(f"Failed to get current context: {e}")
            return self.last_context
    
    def send_suggestion(self, suggestion: Dict[str, Any]) -> bool:
        """发送建议到Cursor"""
        if not self.is_connected:
            return False
        
        try:
            # 格式化建议
            formatted_suggestion = self._format_suggestion(suggestion)
            
            # 发送到Cursor（通过文件或API）
            return self._send_to_cursor(formatted_suggestion)
            
        except Exception as e:
            self.logger.error(f"Failed to send suggestion: {e}")
            return False
    
    def optimize_current_file(self) -> Dict[str, Any]:
        """优化当前文件"""
        context = self.get_current_context()
        if not context or not context.active_file:
            return {'success': False, 'error': 'No active file'}
        
        try:
            # 读取文件内容
            with open(context.active_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 这里可以调用Augment的优化功能
            optimization_result = self._optimize_content(content, context)
            
            return {
                'success': True,
                'file': context.active_file,
                'optimization': optimization_result
            }
            
        except Exception as e:
            self.logger.error(f"Failed to optimize current file: {e}")
            return {'success': False, 'error': str(e)}
    
    def _detect_cursor_ide(self) -> bool:
        """检测Cursor IDE是否运行"""
        try:
            import psutil
            
            # 查找Cursor进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'cursor' in proc.info['name'].lower():
                        self.logger.info(f"Found Cursor process: {proc.info['name']}")
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return False
            
        except ImportError:
            self.logger.warning("psutil not available, cannot detect Cursor IDE")
            return False
        except Exception as e:
            self.logger.error(f"Error detecting Cursor IDE: {e}")
            return False
    
    def _fetch_cursor_context(self) -> Optional[CursorContext]:
        """从Cursor获取上下文信息"""
        try:
            # 这里可以通过多种方式获取Cursor上下文：
            # 1. 读取Cursor的状态文件
            # 2. 通过LSP协议通信
            # 3. 通过文件系统监控
            # 4. 通过剪贴板或临时文件
            
            # 简化实现：检查工作区中的活动文件
            if self.workspace_path:
                workspace = Path(self.workspace_path)
                
                # 查找最近修改的文件
                recent_files = []
                for file_path in workspace.rglob('*.py'):
                    if file_path.is_file():
                        recent_files.append((file_path, file_path.stat().st_mtime))
                
                if recent_files:
                    # 按修改时间排序
                    recent_files.sort(key=lambda x: x[1], reverse=True)
                    active_file = str(recent_files[0][0])
                    
                    return CursorContext(
                        workspace_path=self.workspace_path,
                        active_file=active_file,
                        open_files=[str(f[0]) for f in recent_files[:10]]
                    )
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to fetch Cursor context: {e}")
            return None
    
    def _format_suggestion(self, suggestion: Dict[str, Any]) -> str:
        """格式化建议"""
        suggestion_text = f"💡 Augment建议:\n"
        
        if 'type' in suggestion:
            suggestion_text += f"类型: {suggestion['type']}\n"
        
        if 'message' in suggestion:
            suggestion_text += f"消息: {suggestion['message']}\n"
        
        if 'code' in suggestion:
            suggestion_text += f"代码建议:\n```\n{suggestion['code']}\n```\n"
        
        if 'optimization' in suggestion:
            opt = suggestion['optimization']
            suggestion_text += f"优化效果: 减少{opt.get('token_reduction', 0)}个token\n"
        
        return suggestion_text
    
    def _send_to_cursor(self, content: str) -> bool:
        """发送内容到Cursor"""
        try:
            # 方法1: 写入临时文件，让Cursor读取
            temp_file = Path.home() / '.augment' / 'cursor_suggestions.txt'
            temp_file.parent.mkdir(exist_ok=True)
            
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.debug(f"Suggestion written to {temp_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send to Cursor: {e}")
            return False
    
    def _optimize_content(self, content: str, context: CursorContext) -> Dict[str, Any]:
        """优化内容"""
        # 这里可以调用Augment的各种优化功能
        # 简化实现：基本的代码分析
        
        lines = content.split('\n')
        
        optimization = {
            'original_lines': len(lines),
            'original_tokens': len(content) // 4,
            'suggestions': []
        }
        
        # 检查长函数
        current_function = None
        function_lines = 0
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            if stripped.startswith('def '):
                if current_function and function_lines > 50:
                    optimization['suggestions'].append({
                        'type': 'long_function',
                        'line': i - function_lines,
                        'message': f'函数 {current_function} 过长 ({function_lines} 行)，建议拆分'
                    })
                
                current_function = stripped.split('(')[0].replace('def ', '')
                function_lines = 0
            
            if current_function:
                function_lines += 1
        
        # 检查导入语句
        import_count = len([line for line in lines if line.strip().startswith(('import ', 'from '))])
        if import_count > 20:
            optimization['suggestions'].append({
                'type': 'too_many_imports',
                'message': f'导入语句过多 ({import_count} 个)，建议整理'
            })
        
        # 计算优化潜力
        potential_reduction = len(optimization['suggestions']) * 0.1
        optimization['potential_token_reduction'] = int(optimization['original_tokens'] * potential_reduction)
        
        return optimization
    
    def _trigger_optimization(self, context: CursorContext):
        """触发优化"""
        if not context.active_file:
            return
        
        try:
            # 异步执行优化
            threading.Thread(
                target=self._async_optimize,
                args=(context,),
                daemon=True
            ).start()
            
        except Exception as e:
            self.logger.error(f"Failed to trigger optimization: {e}")
    
    def _async_optimize(self, context: CursorContext):
        """异步优化"""
        try:
            result = self.optimize_current_file()
            
            if result['success'] and result.get('optimization', {}).get('suggestions'):
                # 发送优化建议
                suggestion = {
                    'type': 'optimization',
                    'message': f"发现 {len(result['optimization']['suggestions'])} 个优化建议",
                    'optimization': result['optimization']
                }
                
                self.send_suggestion(suggestion)
            
        except Exception as e:
            self.logger.error(f"Async optimization failed: {e}")
    
    def _start_monitoring(self):
        """启动监控线程"""
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        
        self.logger.info("Started Cursor monitoring thread")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 获取当前上下文
                current_context = self._fetch_cursor_context()
                
                if current_context and current_context != self.last_context:
                    # 上下文发生变化，触发相应处理
                    self._handle_context_change(current_context)
                    self.last_context = current_context
                
                time.sleep(self.context_update_interval)
                
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                time.sleep(self.context_update_interval)
    
    def _handle_context_change(self, new_context: CursorContext):
        """处理上下文变化"""
        if self.auto_optimize and new_context.active_file:
            # 文件切换时触发优化
            self._trigger_optimization(new_context)
    
    def get_integration_status(self) -> Dict[str, Any]:
        """获取集成状态"""
        return {
            'connected': self.is_connected,
            'workspace_path': self.workspace_path,
            'auto_optimize': self.auto_optimize,
            'real_time_suggestions': self.real_time_suggestions,
            'monitoring_active': self.monitoring_active,
            'last_context': {
                'active_file': self.last_context.active_file if self.last_context else None,
                'open_files_count': len(self.last_context.open_files) if self.last_context else 0
            } if self.last_context else None
        }
