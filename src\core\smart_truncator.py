"""
智能截断算法

实现保留最重要信息的智能截断功能，支持代码结构感知和可配置的截断策略。
"""

import logging
import re
import ast
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum


class TruncationStrategy(Enum):
    """截断策略枚举"""
    IMPORTANCE_BASED = "importance_based"    # 基于重要性
    STRUCTURE_AWARE = "structure_aware"      # 结构感知
    HYBRID = "hybrid"                        # 混合策略


@dataclass
class TruncationConfig:
    """截断配置"""
    preserve_imports: bool = True
    preserve_classes: bool = True
    preserve_functions: bool = True
    preserve_comments: bool = False
    preserve_docstrings: bool = True
    preserve_error_handling: bool = True
    min_content_ratio: float = 0.1  # 最小保留内容比例
    max_line_length: int = 120      # 最大行长度


@dataclass
class CodeBlock:
    """代码块"""
    content: str
    start_line: int
    end_line: int
    block_type: str  # 'import', 'class', 'function', 'comment', 'code'
    importance_score: float = 0.0
    dependencies: Set[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = set()
    
    @property
    def line_count(self) -> int:
        """获取行数"""
        return self.end_line - self.start_line + 1
    
    @property
    def token_count(self) -> int:
        """估算token数量"""
        return len(self.content) // 4


class SmartTruncator:
    """智能截断器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化智能截断器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        optimization_config = self.config.get('optimization', {})
        truncation_config = optimization_config.get('smart_truncation', {})
        
        # 初始化截断配置
        self.truncation_config = TruncationConfig(
            preserve_imports=truncation_config.get('preserve_imports', True),
            preserve_classes=truncation_config.get('preserve_classes', True),
            preserve_functions=truncation_config.get('preserve_functions', True),
            preserve_comments=truncation_config.get('preserve_comments', False),
            preserve_docstrings=truncation_config.get('preserve_docstrings', True),
            preserve_error_handling=truncation_config.get('preserve_error_handling', True)
        )
        
        self.logger = logging.getLogger(__name__)
        
        # 重要性模式映射
        self.importance_patterns = {
            'critical': [
                r'class\s+\w+.*:',
                r'def\s+__init__\s*\(',
                r'def\s+main\s*\(',
                r'if\s+__name__\s*==\s*["\']__main__["\']',
                r'raise\s+\w+',
                r'except\s+\w+',
                r'@app\.(route|get|post|put|delete)',
                r'@\w+\.(route|endpoint)'
            ],
            'high': [
                r'def\s+\w+\s*\(',
                r'class\s+\w+',
                r'try\s*:',
                r'finally\s*:',
                r'async\s+def',
                r'@\w+',
                r'import\s+\w+',
                r'from\s+\w+\s+import'
            ],
            'medium': [
                r'return\s+',
                r'yield\s+',
                r'assert\s+',
                r'global\s+',
                r'nonlocal\s+',
                r'lambda\s+'
            ],
            'low': [
                r'^\s*#.*',
                r'^\s*""".*"""',
                r'^\s*\'\'\'.*\'\'\'',
                r'pass\s*$',
                r'^\s*$'
            ]
        }
    
    def truncate_content(self, content: str, target_tokens: int, 
                        strategy: TruncationStrategy = TruncationStrategy.HYBRID) -> str:
        """
        智能截断内容
        
        Args:
            content: 原始内容
            target_tokens: 目标token数量
            strategy: 截断策略
            
        Returns:
            截断后的内容
        """
        current_tokens = len(content) // 4  # 简单估算
        
        if current_tokens <= target_tokens:
            return content
        
        self.logger.info(f"Truncating content: {current_tokens} -> {target_tokens} tokens")
        
        if strategy == TruncationStrategy.IMPORTANCE_BASED:
            return self._truncate_by_importance(content, target_tokens)
        elif strategy == TruncationStrategy.STRUCTURE_AWARE:
            return self._truncate_by_structure(content, target_tokens)
        else:  # HYBRID
            return self._truncate_hybrid(content, target_tokens)
    
    def _truncate_by_importance(self, content: str, target_tokens: int) -> str:
        """基于重要性截断"""
        lines = content.split('\n')
        line_scores = []
        
        # 计算每行的重要性评分
        for i, line in enumerate(lines):
            score = self._calculate_line_importance(line)
            line_scores.append((i, line, score))
        
        # 按重要性排序
        line_scores.sort(key=lambda x: x[2], reverse=True)
        
        # 选择最重要的行直到达到目标token数
        selected_lines = []
        current_tokens = 0
        
        for line_idx, line, score in line_scores:
            line_tokens = len(line) // 4
            if current_tokens + line_tokens <= target_tokens:
                selected_lines.append((line_idx, line))
                current_tokens += line_tokens
            else:
                break
        
        # 按原始顺序重新排列
        selected_lines.sort(key=lambda x: x[0])
        
        return '\n'.join([line for _, line in selected_lines])
    
    def _truncate_by_structure(self, content: str, target_tokens: int) -> str:
        """基于结构截断"""
        try:
            # 尝试解析Python代码结构
            blocks = self._parse_code_blocks(content)
        except:
            # 如果解析失败，回退到基于重要性的截断
            return self._truncate_by_importance(content, target_tokens)
        
        # 按重要性和配置过滤代码块
        filtered_blocks = self._filter_blocks_by_config(blocks)
        
        # 按重要性排序
        filtered_blocks.sort(key=lambda x: x.importance_score, reverse=True)
        
        # 选择代码块直到达到目标token数
        selected_blocks = []
        current_tokens = 0
        
        for block in filtered_blocks:
            if current_tokens + block.token_count <= target_tokens:
                selected_blocks.append(block)
                current_tokens += block.token_count
            else:
                break
        
        # 按原始顺序重新排列
        selected_blocks.sort(key=lambda x: x.start_line)
        
        return '\n'.join([block.content for block in selected_blocks])
    
    def _truncate_hybrid(self, content: str, target_tokens: int) -> str:
        """混合策略截断"""
        try:
            # 首先尝试结构感知截断
            structure_result = self._truncate_by_structure(content, target_tokens)
            structure_tokens = len(structure_result) // 4
            
            # 如果结构截断效果不好，使用重要性截断
            if structure_tokens < target_tokens * 0.8:
                importance_result = self._truncate_by_importance(content, target_tokens)
                importance_tokens = len(importance_result) // 4
                
                # 选择更好的结果
                if importance_tokens > structure_tokens:
                    return importance_result
            
            return structure_result
            
        except Exception as e:
            self.logger.warning(f"Hybrid truncation failed, falling back to importance-based: {e}")
            return self._truncate_by_importance(content, target_tokens)
    
    def _parse_code_blocks(self, content: str) -> List[CodeBlock]:
        """解析代码块"""
        blocks = []
        lines = content.split('\n')
        
        try:
            # 尝试使用AST解析
            tree = ast.parse(content)
            blocks.extend(self._extract_ast_blocks(tree, lines))
        except SyntaxError:
            # 如果AST解析失败，使用正则表达式
            blocks.extend(self._extract_regex_blocks(lines))
        
        return blocks
    
    def _extract_ast_blocks(self, tree: ast.AST, lines: List[str]) -> List[CodeBlock]:
        """从AST提取代码块"""
        blocks = []
        
        for node in ast.walk(tree):
            if hasattr(node, 'lineno') and hasattr(node, 'end_lineno'):
                start_line = node.lineno - 1  # AST行号从1开始
                end_line = node.end_lineno - 1 if node.end_lineno else start_line
                
                if start_line < len(lines) and end_line < len(lines):
                    content = '\n'.join(lines[start_line:end_line + 1])
                    
                    if isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                        block_type = 'import'
                    elif isinstance(node, ast.ClassDef):
                        block_type = 'class'
                    elif isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                        block_type = 'function'
                    else:
                        block_type = 'code'
                    
                    block = CodeBlock(
                        content=content,
                        start_line=start_line,
                        end_line=end_line,
                        block_type=block_type
                    )
                    block.importance_score = self._calculate_block_importance(block)
                    blocks.append(block)
        
        return blocks
    
    def _extract_regex_blocks(self, lines: List[str]) -> List[CodeBlock]:
        """使用正则表达式提取代码块"""
        blocks = []
        current_block = None
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # 检测块的开始
            if re.match(r'^\s*(import|from)\s+', line):
                if current_block:
                    blocks.append(current_block)
                current_block = CodeBlock(
                    content=line,
                    start_line=i,
                    end_line=i,
                    block_type='import'
                )
            elif re.match(r'^\s*class\s+\w+', line):
                if current_block:
                    blocks.append(current_block)
                current_block = CodeBlock(
                    content=line,
                    start_line=i,
                    end_line=i,
                    block_type='class'
                )
            elif re.match(r'^\s*def\s+\w+', line):
                if current_block:
                    blocks.append(current_block)
                current_block = CodeBlock(
                    content=line,
                    start_line=i,
                    end_line=i,
                    block_type='function'
                )
            elif re.match(r'^\s*#', line):
                if current_block and current_block.block_type != 'comment':
                    blocks.append(current_block)
                    current_block = CodeBlock(
                        content=line,
                        start_line=i,
                        end_line=i,
                        block_type='comment'
                    )
                elif current_block and current_block.block_type == 'comment':
                    current_block.content += '\n' + line
                    current_block.end_line = i
                else:
                    current_block = CodeBlock(
                        content=line,
                        start_line=i,
                        end_line=i,
                        block_type='comment'
                    )
            else:
                # 普通代码行
                if current_block and current_block.block_type in ['class', 'function', 'code']:
                    current_block.content += '\n' + line
                    current_block.end_line = i
                else:
                    if current_block:
                        blocks.append(current_block)
                    current_block = CodeBlock(
                        content=line,
                        start_line=i,
                        end_line=i,
                        block_type='code'
                    )
        
        if current_block:
            blocks.append(current_block)
        
        # 计算重要性评分
        for block in blocks:
            block.importance_score = self._calculate_block_importance(block)
        
        return blocks
    
    def _filter_blocks_by_config(self, blocks: List[CodeBlock]) -> List[CodeBlock]:
        """根据配置过滤代码块"""
        filtered = []
        
        for block in blocks:
            should_preserve = True
            
            if block.block_type == 'import' and not self.truncation_config.preserve_imports:
                should_preserve = False
            elif block.block_type == 'class' and not self.truncation_config.preserve_classes:
                should_preserve = False
            elif block.block_type == 'function' and not self.truncation_config.preserve_functions:
                should_preserve = False
            elif block.block_type == 'comment' and not self.truncation_config.preserve_comments:
                should_preserve = False
            
            # 检查错误处理
            if (self.truncation_config.preserve_error_handling and 
                any(keyword in block.content.lower() for keyword in ['try', 'except', 'finally', 'raise'])):
                should_preserve = True
            
            if should_preserve:
                filtered.append(block)
        
        return filtered
    
    def _calculate_line_importance(self, line: str) -> float:
        """计算行的重要性评分"""
        line_stripped = line.strip()
        
        if not line_stripped:
            return 0.0
        
        # 检查重要性模式
        for importance_level, patterns in self.importance_patterns.items():
            for pattern in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    if importance_level == 'critical':
                        return 1.0
                    elif importance_level == 'high':
                        return 0.8
                    elif importance_level == 'medium':
                        return 0.6
                    else:  # low
                        return 0.2
        
        # 默认评分
        return 0.4
    
    def _calculate_block_importance(self, block: CodeBlock) -> float:
        """计算代码块的重要性评分"""
        base_scores = {
            'import': 0.7,
            'class': 0.9,
            'function': 0.8,
            'comment': 0.1,
            'code': 0.5
        }
        
        base_score = base_scores.get(block.block_type, 0.5)
        
        # 根据内容调整评分
        content_lower = block.content.lower()
        
        # 关键字加分
        if any(keyword in content_lower for keyword in ['main', 'init', 'error', 'exception']):
            base_score += 0.2
        
        if any(keyword in content_lower for keyword in ['try', 'except', 'finally']):
            base_score += 0.1
        
        if any(keyword in content_lower for keyword in ['api', 'endpoint', 'route']):
            base_score += 0.15
        
        # 文档字符串加分
        if '"""' in block.content or "'''" in block.content:
            if self.truncation_config.preserve_docstrings:
                base_score += 0.1
        
        return min(base_score, 1.0)
    
    def get_truncation_summary(self, original_content: str, truncated_content: str) -> Dict[str, Any]:
        """
        获取截断摘要
        
        Args:
            original_content: 原始内容
            truncated_content: 截断后内容
            
        Returns:
            截断摘要
        """
        original_lines = len(original_content.split('\n'))
        truncated_lines = len(truncated_content.split('\n'))
        original_tokens = len(original_content) // 4
        truncated_tokens = len(truncated_content) // 4
        
        return {
            'original_lines': original_lines,
            'truncated_lines': truncated_lines,
            'lines_reduction': original_lines - truncated_lines,
            'lines_reduction_ratio': (original_lines - truncated_lines) / original_lines if original_lines > 0 else 0,
            'original_tokens': original_tokens,
            'truncated_tokens': truncated_tokens,
            'tokens_reduction': original_tokens - truncated_tokens,
            'tokens_reduction_ratio': (original_tokens - truncated_tokens) / original_tokens if original_tokens > 0 else 0
        }
