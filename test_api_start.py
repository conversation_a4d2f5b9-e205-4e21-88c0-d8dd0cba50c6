#!/usr/bin/env python3
"""
测试API启动脚本 - 最简化版本
"""

print("=== 开始测试API启动 ===")

# 测试1: 基础导入
print("1. 测试基础模块导入...")
try:
    import os
    import sys
    from pathlib import Path
    print("✅ 基础模块导入成功")
except Exception as e:
    print(f"❌ 基础模块导入失败: {e}")
    exit(1)

# 测试2: 添加路径
print("2. 添加src到Python路径...")
sys.path.append('src')
print(f"✅ 当前Python路径: {sys.path[-1]}")

# 测试3: uvicorn导入
print("3. 测试uvicorn导入...")
try:
    import uvicorn
    print("✅ uvicorn导入成功")
except Exception as e:
    print(f"❌ uvicorn导入失败: {e}")
    exit(1)

# 测试4: FastAPI导入
print("4. 测试FastAPI导入...")
try:
    from fastapi import FastAPI
    print("✅ FastAPI导入成功")
except Exception as e:
    print(f"❌ FastAPI导入失败: {e}")
    exit(1)

# 测试5: 创建简单应用
print("5. 创建简单API应用...")
try:
    app = FastAPI(title="测试API")
    
    @app.get("/health")
    def health():
        return {"status": "healthy", "message": "API测试成功"}
    
    print("✅ 简单API应用创建成功")
except Exception as e:
    print(f"❌ API应用创建失败: {e}")
    exit(1)

# 测试6: 启动服务
print("6. 启动API服务...")
print(">>> 绑定地址: 127.0.0.1:8888")
print(">>> 健康检查: http://127.0.0.1:8888/health")

try:
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8888,
        reload=False,
        log_level="info"
    )
except Exception as e:
    print(f"❌ API服务启动失败: {e}")
    import traceback
    traceback.print_exc()
    exit(1)
