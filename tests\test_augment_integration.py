"""
Augment系统集成测试

测试Token预算管理、分层上下文管理、智能截断等核心功能的集成。
"""

import unittest
import tempfile
import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.config.augment_config import AugmentConfigLoader, AugmentConfig
from src.core.token_manager import TokenBudgetManager, ContextItem
from src.core.context_manager import ContextManager, ContextCategory
from src.core.priority_engine import PriorityEngine
from src.core.smart_truncator import SmartTruncator, TruncationStrategy
from src.core.cache.context_cache import LayeredCacheManager


class TestAugmentIntegration(unittest.TestCase):
    """Augment系统集成测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时配置
        self.test_config = {
            'augment': {
                'context_management': {
                    'max_tokens': 10000,
                    'warning_threshold': 0.8,
                    'critical_threshold': 0.95,
                    'context_layers': {
                        'core_priority': 0.6,
                        'related_priority': 0.3,
                        'background_priority': 0.1
                    }
                },
                'optimization': {
                    'auto_adjustment': True,
                    'smart_truncation': {
                        'enabled': True,
                        'preserve_imports': True,
                        'preserve_classes': True,
                        'preserve_functions': True
                    }
                },
                'cache': {
                    'layered_cache_enabled': True,
                    'cache_ttl': 3600,
                    'cache_file': tempfile.mktemp(suffix='.db')
                }
            }
        }
        
        # 初始化组件
        self.token_manager = TokenBudgetManager(self.test_config)
        self.context_manager = ContextManager(self.test_config)
        self.priority_engine = PriorityEngine(self.test_config)
        self.smart_truncator = SmartTruncator(self.test_config)
        self.cache_manager = LayeredCacheManager(self.test_config)
        
        # 创建测试数据
        self.test_items = [
            ContextItem(
                content="import os\nimport sys\nfrom typing import Dict",
                priority=0.7,
                category="related",
                file_path="test.py",
                token_count=50
            ),
            ContextItem(
                content="class TokenManager:\n    def __init__(self):\n        pass",
                priority=0.9,
                category="core",
                file_path="core.py",
                token_count=200
            ),
            ContextItem(
                content="# This is a comment\n# Another comment",
                priority=0.2,
                category="background",
                file_path="utils.py",
                token_count=30
            ),
            ContextItem(
                content="def process_data(data):\n    return data.strip()",
                priority=0.6,
                category="related",
                file_path="utils.py",
                token_count=80
            )
        ]
    
    def test_token_budget_management(self):
        """测试Token预算管理"""
        # 测试token计数
        test_content = "def hello_world():\n    print('Hello, World!')"
        token_count = self.token_manager.count_tokens(test_content)
        self.assertGreater(token_count, 0)
        
        # 测试预算状态检查
        status, ratio = self.token_manager.check_budget_status(8000)  # 80%
        self.assertEqual(status, "warning")
        
        status, ratio = self.token_manager.check_budget_status(9500)  # 95%
        self.assertEqual(status, "critical")
        
        # 测试智能截断
        truncated = self.token_manager.smart_truncate(self.test_items, 200)
        total_tokens = sum(item.token_count for item in truncated)
        self.assertLessEqual(total_tokens, 200)
        
        # 验证核心内容被保留
        core_items = [item for item in truncated if item.category == "core"]
        self.assertGreater(len(core_items), 0)
    
    def test_context_management(self):
        """测试上下文管理"""
        # 添加测试项目
        for item in self.test_items:
            self.context_manager.add_context_item(item)
        
        # 测试优化后的上下文获取
        optimized = self.context_manager.get_optimized_context(300)
        total_tokens = sum(item.token_count for item in optimized)
        self.assertLessEqual(total_tokens, 300)
        
        # 验证分层结构
        categories = set(item.category for item in optimized)
        self.assertIn("core", categories)  # 核心内容应该被保留
        
        # 测试统计信息
        stats = self.context_manager.get_statistics()
        self.assertIn('core', stats)
        self.assertIn('related', stats)
        self.assertIn('background', stats)
    
    def test_priority_engine(self):
        """测试优先级引擎"""
        # 测试重要性评分计算
        for item in self.test_items:
            score = self.priority_engine.calculate_importance_score(item)
            self.assertGreaterEqual(score, 0.0)
            self.assertLessEqual(score, 1.0)
            item.importance_score = score
        
        # 验证核心内容得分更高
        core_items = [item for item in self.test_items if item.category == "core"]
        background_items = [item for item in self.test_items if item.category == "background"]
        
        if core_items and background_items:
            avg_core_score = sum(item.importance_score for item in core_items) / len(core_items)
            avg_bg_score = sum(item.importance_score for item in background_items) / len(background_items)
            self.assertGreater(avg_core_score, avg_bg_score)
        
        # 测试用户偏好学习
        test_item = self.test_items[0]
        self.priority_engine.update_user_preferences(test_item, 0.8)
        self.priority_engine.learn_from_access_pattern(test_item)
    
    def test_smart_truncation(self):
        """测试智能截断"""
        # 创建测试代码
        test_code = '''
import os
import sys

class TestClass:
    def __init__(self):
        self.value = 0
    
    def method(self):
        return self.value

def function():
    # This is a comment
    return "hello"

if __name__ == "__main__":
    test = TestClass()
    print(test.method())
'''
        
        # 测试不同截断策略
        strategies = [
            TruncationStrategy.IMPORTANCE_BASED,
            TruncationStrategy.STRUCTURE_AWARE,
            TruncationStrategy.HYBRID
        ]
        
        for strategy in strategies:
            truncated = self.smart_truncator.truncate_content(test_code, 200, strategy)
            self.assertLess(len(truncated), len(test_code))
            
            # 验证重要结构被保留
            self.assertIn("class TestClass", truncated)  # 类定义应该被保留
        
        # 测试截断摘要
        truncated = self.smart_truncator.truncate_content(test_code, 200)
        summary = self.smart_truncator.get_truncation_summary(test_code, truncated)
        
        self.assertIn('original_tokens', summary)
        self.assertIn('truncated_tokens', summary)
        self.assertIn('tokens_reduction_ratio', summary)
        self.assertGreater(summary['tokens_reduction_ratio'], 0)
    
    def test_cache_integration(self):
        """测试缓存集成"""
        # 测试缓存设置和获取
        test_key = "test_context"
        test_value = {"content": "test", "tokens": 100}
        
        self.cache_manager.set(test_key, test_value, "core")
        cached_value = self.cache_manager.get(test_key, "core")
        
        self.assertEqual(cached_value, test_value)
        
        # 测试不同类别的缓存
        categories = ["core", "related", "background"]
        for category in categories:
            key = f"test_{category}"
            value = {"category": category, "data": "test"}
            
            self.cache_manager.set(key, value, category)
            retrieved = self.cache_manager.get(key, category)
            self.assertEqual(retrieved, value)
        
        # 测试缓存统计
        stats = self.cache_manager.get_statistics()
        self.assertTrue(stats['enabled'])
        self.assertGreater(stats['memory_cache_size'], 0)
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        # 1. 添加上下文项目到管理器
        for item in self.test_items:
            # 计算重要性评分
            item.importance_score = self.priority_engine.calculate_importance_score(item)
            # 添加到上下文管理器
            self.context_manager.add_context_item(item)
        
        # 2. 获取优化后的上下文
        target_tokens = 250
        optimized_context = self.context_manager.get_optimized_context(target_tokens)
        
        # 3. 检查token预算
        total_tokens = sum(item.token_count for item in optimized_context)
        status, ratio = self.token_manager.check_budget_status(total_tokens)
        
        # 4. 如果需要，执行智能截断
        if total_tokens > target_tokens:
            # 合并内容进行截断
            combined_content = "\n".join(item.content for item in optimized_context)
            truncated_content = self.smart_truncator.truncate_content(
                combined_content, target_tokens
            )
            
            # 验证截断效果
            truncated_tokens = len(truncated_content) // 4
            self.assertLessEqual(truncated_tokens, target_tokens)
        
        # 5. 缓存结果
        cache_key = self.cache_manager.get_cache_key(
            str(optimized_context), "optimized_context"
        )
        self.cache_manager.set(cache_key, optimized_context, "core")
        
        # 6. 验证缓存
        cached_result = self.cache_manager.get(cache_key, "core")
        self.assertIsNotNone(cached_result)
        
        # 7. 更新使用统计
        self.token_manager.update_usage_stats(optimized_context, 1.5)
        stats = self.token_manager.get_usage_stats()
        
        self.assertIn('current', stats)
        self.assertGreater(stats['current']['total_tokens'], 0)
    
    def test_performance_under_load(self):
        """测试负载下的性能"""
        # 创建大量测试数据
        large_test_items = []
        for i in range(100):
            item = ContextItem(
                content=f"def function_{i}():\n    return {i}",
                priority=0.5 + (i % 5) * 0.1,
                category=["core", "related", "background"][i % 3],
                file_path=f"file_{i}.py",
                token_count=50 + i
            )
            large_test_items.append(item)
        
        # 测试批量处理性能
        start_time = datetime.now()
        
        for item in large_test_items:
            item.importance_score = self.priority_engine.calculate_importance_score(item)
            self.context_manager.add_context_item(item)
        
        optimized = self.context_manager.get_optimized_context(5000)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # 验证性能（应该在合理时间内完成）
        self.assertLess(processing_time, 10.0)  # 10秒内完成
        self.assertGreater(len(optimized), 0)
        
        # 验证结果质量
        total_tokens = sum(item.token_count for item in optimized)
        self.assertLessEqual(total_tokens, 5000)
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        cache_file = self.test_config['augment']['cache']['cache_file']
        if os.path.exists(cache_file):
            os.remove(cache_file)


if __name__ == '__main__':
    unittest.main()
