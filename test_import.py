#!/usr/bin/env python3
"""
测试导入问题
"""

import sys
import os
from pathlib import Path

print("🔍 开始导入测试...")

# 添加src到Python路径
sys.path.append('src')
print(f"✅ Python路径已添加src: {sys.path[-1]}")

try:
    print("🔍 测试基础导入...")
    from fastapi import FastAPI
    print("✅ FastAPI导入成功")
except Exception as e:
    print(f"❌ FastAPI导入失败: {e}")
    exit(1)

try:
    print("🔍 测试Bug检测系统导入...")
    from bug_detection.core.database_manager import DatabaseManager
    print("✅ Bug检测系统导入成功")
except Exception as e:
    print(f"❌ Bug检测系统导入失败: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

try:
    print("🔍 测试production_main导入...")
    from api.production_main import app
    print("✅ production_main导入成功")
except Exception as e:
    print(f"❌ production_main导入失败: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

print("🎉 所有导入测试通过！")
