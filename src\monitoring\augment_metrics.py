"""
Augment性能监控系统

监控压缩效果、用户满意度、系统性能等关键指标。
"""

import logging
import time
import json
import os
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque
from enum import Enum
import threading
import numpy as np


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class MetricValue:
    """指标值"""
    value: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'value': self.value,
            'timestamp': self.timestamp.isoformat(),
            'tags': self.tags
        }


@dataclass
class MetricSummary:
    """指标摘要"""
    name: str
    metric_type: MetricType
    count: int
    min_value: float
    max_value: float
    avg_value: float
    latest_value: float
    latest_timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'type': self.metric_type.value,
            'count': self.count,
            'min': self.min_value,
            'max': self.max_value,
            'avg': self.avg_value,
            'latest': self.latest_value,
            'timestamp': self.latest_timestamp.isoformat()
        }


class MetricCollector:
    """指标收集器"""
    
    def __init__(self, max_values: int = 1000):
        """
        初始化指标收集器
        
        Args:
            max_values: 最大保存值数量
        """
        self.max_values = max_values
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_values))
        self.metric_types: Dict[str, MetricType] = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
    
    def record_counter(self, name: str, value: float = 1.0, tags: Optional[Dict[str, str]] = None):
        """记录计数器指标"""
        self._record_metric(name, MetricType.COUNTER, value, tags)
    
    def record_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """记录仪表指标"""
        self._record_metric(name, MetricType.GAUGE, value, tags)
    
    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """记录直方图指标"""
        self._record_metric(name, MetricType.HISTOGRAM, value, tags)
    
    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None):
        """记录计时器指标"""
        self._record_metric(name, MetricType.TIMER, duration, tags)
    
    def _record_metric(self, name: str, metric_type: MetricType, value: float, 
                      tags: Optional[Dict[str, str]] = None):
        """记录指标"""
        with self.lock:
            metric_value = MetricValue(
                value=value,
                timestamp=datetime.now(),
                tags=tags or {}
            )
            
            self.metrics[name].append(metric_value)
            self.metric_types[name] = metric_type
    
    def get_metric_summary(self, name: str) -> Optional[MetricSummary]:
        """获取指标摘要"""
        with self.lock:
            if name not in self.metrics or not self.metrics[name]:
                return None
            
            values = [mv.value for mv in self.metrics[name]]
            latest = self.metrics[name][-1]
            
            return MetricSummary(
                name=name,
                metric_type=self.metric_types[name],
                count=len(values),
                min_value=min(values),
                max_value=max(values),
                avg_value=np.mean(values),
                latest_value=latest.value,
                latest_timestamp=latest.timestamp
            )
    
    def get_all_summaries(self) -> Dict[str, MetricSummary]:
        """获取所有指标摘要"""
        summaries = {}
        for name in self.metrics.keys():
            summary = self.get_metric_summary(name)
            if summary:
                summaries[name] = summary
        return summaries
    
    def get_recent_values(self, name: str, minutes: int = 60) -> List[MetricValue]:
        """获取最近的指标值"""
        with self.lock:
            if name not in self.metrics:
                return []
            
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            return [mv for mv in self.metrics[name] if mv.timestamp > cutoff_time]


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, collector: MetricCollector):
        """
        初始化性能分析器
        
        Args:
            collector: 指标收集器
        """
        self.collector = collector
        self.logger = logging.getLogger(__name__)
    
    def analyze_compression_effectiveness(self) -> Dict[str, Any]:
        """分析压缩效果"""
        compression_ratio_summary = self.collector.get_metric_summary('compression_ratio')
        token_reduction_summary = self.collector.get_metric_summary('token_reduction')
        
        analysis = {
            'compression_enabled': compression_ratio_summary is not None,
            'effectiveness': 'unknown'
        }
        
        if compression_ratio_summary:
            avg_ratio = compression_ratio_summary.avg_value
            analysis.update({
                'average_compression_ratio': avg_ratio,
                'best_compression': compression_ratio_summary.min_value,
                'worst_compression': compression_ratio_summary.max_value,
                'total_compressions': compression_ratio_summary.count
            })
            
            # 评估效果
            if avg_ratio < 0.5:
                analysis['effectiveness'] = 'excellent'
            elif avg_ratio < 0.7:
                analysis['effectiveness'] = 'good'
            elif avg_ratio < 0.9:
                analysis['effectiveness'] = 'fair'
            else:
                analysis['effectiveness'] = 'poor'
        
        if token_reduction_summary:
            analysis['average_token_reduction'] = token_reduction_summary.avg_value
        
        return analysis
    
    def analyze_user_satisfaction(self) -> Dict[str, Any]:
        """分析用户满意度"""
        satisfaction_summary = self.collector.get_metric_summary('user_satisfaction')
        response_time_summary = self.collector.get_metric_summary('response_time')
        
        analysis = {
            'satisfaction_tracked': satisfaction_summary is not None,
            'overall_rating': 'unknown'
        }
        
        if satisfaction_summary:
            avg_satisfaction = satisfaction_summary.avg_value
            analysis.update({
                'average_satisfaction': avg_satisfaction,
                'satisfaction_trend': self._calculate_trend('user_satisfaction'),
                'total_ratings': satisfaction_summary.count
            })
            
            # 评估满意度
            if avg_satisfaction >= 0.8:
                analysis['overall_rating'] = 'excellent'
            elif avg_satisfaction >= 0.6:
                analysis['overall_rating'] = 'good'
            elif avg_satisfaction >= 0.4:
                analysis['overall_rating'] = 'fair'
            else:
                analysis['overall_rating'] = 'poor'
        
        if response_time_summary:
            analysis['average_response_time'] = response_time_summary.avg_value
            analysis['response_time_trend'] = self._calculate_trend('response_time')
        
        return analysis
    
    def analyze_system_performance(self) -> Dict[str, Any]:
        """分析系统性能"""
        metrics_to_analyze = [
            'token_usage_ratio',
            'cache_hit_ratio',
            'context_optimization_ratio',
            'truncation_frequency'
        ]
        
        analysis = {}
        
        for metric in metrics_to_analyze:
            summary = self.collector.get_metric_summary(metric)
            if summary:
                analysis[metric] = {
                    'current': summary.latest_value,
                    'average': summary.avg_value,
                    'trend': self._calculate_trend(metric),
                    'status': self._evaluate_metric_status(metric, summary.latest_value)
                }
        
        # 整体性能评分
        analysis['overall_performance'] = self._calculate_overall_performance(analysis)
        
        return analysis
    
    def _calculate_trend(self, metric_name: str, window_minutes: int = 30) -> str:
        """计算趋势"""
        recent_values = self.collector.get_recent_values(metric_name, window_minutes)
        
        if len(recent_values) < 5:
            return 'insufficient_data'
        
        # 简单的线性趋势分析
        values = [mv.value for mv in recent_values]
        timestamps = [(mv.timestamp - recent_values[0].timestamp).total_seconds() 
                     for mv in recent_values]
        
        # 计算斜率
        if len(values) > 1:
            slope = np.polyfit(timestamps, values, 1)[0]
            
            if abs(slope) < 0.001:
                return 'stable'
            elif slope > 0:
                return 'increasing'
            else:
                return 'decreasing'
        
        return 'stable'
    
    def _evaluate_metric_status(self, metric_name: str, value: float) -> str:
        """评估指标状态"""
        thresholds = {
            'token_usage_ratio': {'good': 0.7, 'warning': 0.85, 'critical': 0.95},
            'cache_hit_ratio': {'good': 0.7, 'warning': 0.5, 'critical': 0.3},
            'context_optimization_ratio': {'good': 0.8, 'warning': 0.6, 'critical': 0.4},
            'response_time': {'good': 2.0, 'warning': 5.0, 'critical': 10.0}
        }
        
        if metric_name not in thresholds:
            return 'unknown'
        
        threshold = thresholds[metric_name]
        
        if metric_name == 'response_time':
            # 响应时间越低越好
            if value <= threshold['good']:
                return 'good'
            elif value <= threshold['warning']:
                return 'warning'
            else:
                return 'critical'
        else:
            # 其他指标越高越好
            if value >= threshold['good']:
                return 'good'
            elif value >= threshold['warning']:
                return 'warning'
            else:
                return 'critical'
    
    def _calculate_overall_performance(self, analysis: Dict[str, Any]) -> str:
        """计算整体性能评分"""
        status_scores = {'good': 3, 'warning': 2, 'critical': 1, 'unknown': 0}
        
        total_score = 0
        total_metrics = 0
        
        for metric_data in analysis.values():
            if isinstance(metric_data, dict) and 'status' in metric_data:
                total_score += status_scores.get(metric_data['status'], 0)
                total_metrics += 1
        
        if total_metrics == 0:
            return 'unknown'
        
        avg_score = total_score / total_metrics
        
        if avg_score >= 2.5:
            return 'excellent'
        elif avg_score >= 2.0:
            return 'good'
        elif avg_score >= 1.5:
            return 'fair'
        else:
            return 'poor'


class AugmentMetricsCollector:
    """Augment指标收集器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Augment指标收集器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        monitoring_config = self.config.get('monitoring', {})
        
        self.enabled = monitoring_config.get('real_time_monitoring', True)
        self.collection_interval = monitoring_config.get('metrics_collection_interval', 60)
        self.data_file = 'data/augment_metrics.json'
        
        # 核心组件
        self.collector = MetricCollector()
        self.analyzer = PerformanceAnalyzer(self.collector)
        
        # 定时器
        self.collection_timer: Optional[threading.Timer] = None
        
        self.logger = logging.getLogger(__name__)
        
        if self.enabled:
            self._start_collection()
    
    def record_token_usage(self, current_tokens: int, max_tokens: int, category: str = 'general'):
        """记录token使用情况"""
        if not self.enabled:
            return
        
        usage_ratio = current_tokens / max_tokens if max_tokens > 0 else 0
        
        self.collector.record_gauge('token_usage_ratio', usage_ratio, {'category': category})
        self.collector.record_gauge('current_tokens', current_tokens, {'category': category})
        self.collector.record_counter('token_usage_events', 1, {'category': category})
    
    def record_compression_result(self, original_size: int, compressed_size: int, 
                                compression_time: float):
        """记录压缩结果"""
        if not self.enabled:
            return
        
        compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
        reduction_ratio = 1.0 - compression_ratio
        
        self.collector.record_histogram('compression_ratio', compression_ratio)
        self.collector.record_histogram('token_reduction', reduction_ratio)
        self.collector.record_timer('compression_time', compression_time)
        self.collector.record_counter('compression_events', 1)
    
    def record_context_optimization(self, original_items: int, optimized_items: int,
                                  optimization_time: float):
        """记录上下文优化结果"""
        if not self.enabled:
            return
        
        optimization_ratio = optimized_items / original_items if original_items > 0 else 1.0
        
        self.collector.record_histogram('context_optimization_ratio', optimization_ratio)
        self.collector.record_timer('context_optimization_time', optimization_time)
        self.collector.record_counter('context_optimization_events', 1)
    
    def record_cache_access(self, hit: bool, access_time: float, category: str = 'general'):
        """记录缓存访问"""
        if not self.enabled:
            return
        
        self.collector.record_counter('cache_hits' if hit else 'cache_misses', 1, 
                                     {'category': category})
        self.collector.record_timer('cache_access_time', access_time, {'category': category})
        
        # 计算命中率
        hit_summary = self.collector.get_metric_summary('cache_hits')
        miss_summary = self.collector.get_metric_summary('cache_misses')
        
        if hit_summary and miss_summary:
            total_accesses = hit_summary.count + miss_summary.count
            hit_ratio = hit_summary.count / total_accesses
            self.collector.record_gauge('cache_hit_ratio', hit_ratio, {'category': category})
    
    def record_user_satisfaction(self, satisfaction_score: float, context: Dict[str, Any]):
        """记录用户满意度"""
        if not self.enabled:
            return
        
        tags = {
            'feature': context.get('feature', 'general'),
            'session': context.get('session_id', 'unknown')
        }
        
        self.collector.record_histogram('user_satisfaction', satisfaction_score, tags)
        self.collector.record_counter('satisfaction_ratings', 1, tags)
    
    def record_response_time(self, response_time: float, operation: str):
        """记录响应时间"""
        if not self.enabled:
            return
        
        self.collector.record_timer('response_time', response_time, {'operation': operation})
        
        # 记录性能等级
        if response_time <= 1.0:
            performance_level = 'excellent'
        elif response_time <= 3.0:
            performance_level = 'good'
        elif response_time <= 5.0:
            performance_level = 'fair'
        else:
            performance_level = 'poor'
        
        self.collector.record_counter('performance_levels', 1, 
                                     {'level': performance_level, 'operation': operation})
    
    def record_error(self, error_type: str, error_message: str, context: Dict[str, Any]):
        """记录错误"""
        if not self.enabled:
            return
        
        tags = {
            'error_type': error_type,
            'component': context.get('component', 'unknown')
        }
        
        self.collector.record_counter('errors', 1, tags)
        self.collector.record_counter('error_types', 1, {'type': error_type})
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.enabled:
            return {'enabled': False}
        
        report = {
            'enabled': True,
            'collection_interval': self.collection_interval,
            'timestamp': datetime.now().isoformat(),
            'compression_analysis': self.analyzer.analyze_compression_effectiveness(),
            'satisfaction_analysis': self.analyzer.analyze_user_satisfaction(),
            'performance_analysis': self.analyzer.analyze_system_performance(),
            'metric_summaries': {}
        }
        
        # 添加关键指标摘要
        key_metrics = [
            'token_usage_ratio', 'compression_ratio', 'cache_hit_ratio',
            'user_satisfaction', 'response_time', 'errors'
        ]
        
        for metric in key_metrics:
            summary = self.collector.get_metric_summary(metric)
            if summary:
                report['metric_summaries'][metric] = summary.to_dict()
        
        return report
    
    def export_metrics(self, file_path: str):
        """导出指标数据"""
        if not self.enabled:
            return
        
        export_data = {
            'timestamp': datetime.now().isoformat(),
            'config': self.config,
            'performance_report': self.get_performance_report(),
            'all_summaries': {name: summary.to_dict() 
                            for name, summary in self.collector.get_all_summaries().items()}
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2)
        
        self.logger.info(f"Exported metrics to {file_path}")
    
    def _start_collection(self):
        """开始定时收集"""
        if self.collection_timer:
            self.collection_timer.cancel()
        
        self.collection_timer = threading.Timer(self.collection_interval, self._collect_system_metrics)
        self.collection_timer.start()
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # 收集系统资源使用情况
            import psutil
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent()
            self.collector.record_gauge('system_cpu_usage', cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.collector.record_gauge('system_memory_usage', memory.percent)
            
            # 磁盘使用率
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            self.collector.record_gauge('system_disk_usage', disk_percent)
            
        except ImportError:
            # psutil不可用时跳过系统指标收集
            pass
        except Exception as e:
            self.logger.warning(f"Failed to collect system metrics: {e}")
        
        # 继续下一次收集
        if self.enabled:
            self._start_collection()
    
    def stop_collection(self):
        """停止收集"""
        self.enabled = False
        if self.collection_timer:
            self.collection_timer.cancel()
            self.collection_timer = None
    
    def __del__(self):
        """析构函数"""
        self.stop_collection()
