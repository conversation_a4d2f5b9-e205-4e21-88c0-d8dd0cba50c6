"""
上下文分层缓存系统

支持上下文分层缓存和增量更新，优化Augment系统的性能。
"""

import logging
import pickle
import sqlite3
import hashlib
import json
import os
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    category: str  # 'core', 'related', 'background'
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl: int = 3600  # 生存时间（秒）
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    def update_access(self):
        """更新访问信息"""
        self.last_accessed = datetime.now()
        self.access_count += 1


class LayeredCacheManager:
    """分层缓存管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化缓存管理器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        cache_config = self.config.get('cache', {})
        
        # 缓存配置
        self.enabled = cache_config.get('layered_cache_enabled', True)
        self.default_ttl = cache_config.get('cache_ttl', 3600)
        self.max_cache_size = cache_config.get('max_cache_size', 1000)
        self.cache_file = cache_config.get('cache_file', 'data/augment_cache.db')
        
        # 分层配置
        self.layer_ttls = {
            'core': self.default_ttl * 2,      # 核心内容缓存更久
            'related': self.default_ttl,       # 相关内容标准缓存
            'background': self.default_ttl // 2 # 背景内容缓存较短
        }
        
        # 内存缓存
        self.memory_cache: Dict[str, CacheEntry] = {}
        
        # 数据库连接
        self.db_path = self.cache_file
        self._init_database()
        
        self.logger = logging.getLogger(__name__)
    
    def _init_database(self):
        """初始化数据库"""
        if not self.enabled:
            return
        
        # 确保目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS cache_entries (
                        key TEXT PRIMARY KEY,
                        value BLOB,
                        category TEXT,
                        created_at TEXT,
                        last_accessed TEXT,
                        access_count INTEGER,
                        ttl INTEGER
                    )
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_category ON cache_entries(category)
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_created_at ON cache_entries(created_at)
                ''')
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to initialize cache database: {e}")
    
    def get(self, key: str, category: str = 'background') -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            category: 缓存类别
            
        Returns:
            缓存值或None
        """
        if not self.enabled:
            return None
        
        # 首先检查内存缓存
        if key in self.memory_cache:
            entry = self.memory_cache[key]
            if not entry.is_expired():
                entry.update_access()
                return entry.value
            else:
                del self.memory_cache[key]
        
        # 检查数据库缓存
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT value, category, created_at, last_accessed, access_count, ttl FROM cache_entries WHERE key = ?',
                    (key,)
                )
                row = cursor.fetchone()
                
                if row:
                    value_blob, cat, created_str, accessed_str, access_count, ttl = row
                    created_at = datetime.fromisoformat(created_str)
                    last_accessed = datetime.fromisoformat(accessed_str)
                    
                    entry = CacheEntry(
                        key=key,
                        value=pickle.loads(value_blob),
                        category=cat,
                        created_at=created_at,
                        last_accessed=last_accessed,
                        access_count=access_count,
                        ttl=ttl
                    )
                    
                    if not entry.is_expired():
                        entry.update_access()
                        
                        # 更新数据库中的访问信息
                        conn.execute(
                            'UPDATE cache_entries SET last_accessed = ?, access_count = ? WHERE key = ?',
                            (entry.last_accessed.isoformat(), entry.access_count, key)
                        )
                        conn.commit()
                        
                        # 添加到内存缓存
                        if len(self.memory_cache) < self.max_cache_size // 2:
                            self.memory_cache[key] = entry
                        
                        return entry.value
                    else:
                        # 删除过期条目
                        conn.execute('DELETE FROM cache_entries WHERE key = ?', (key,))
                        conn.commit()
                
        except Exception as e:
            self.logger.warning(f"Failed to get cache entry {key}: {e}")
        
        return None
    
    def set(self, key: str, value: Any, category: str = 'background', ttl: Optional[int] = None):
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            category: 缓存类别
            ttl: 生存时间，如果为None则使用默认值
        """
        if not self.enabled:
            return
        
        if ttl is None:
            ttl = self.layer_ttls.get(category, self.default_ttl)
        
        entry = CacheEntry(
            key=key,
            value=value,
            category=category,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            ttl=ttl
        )
        
        # 添加到内存缓存
        if len(self.memory_cache) < self.max_cache_size:
            self.memory_cache[key] = entry
        
        # 保存到数据库
        try:
            with sqlite3.connect(self.db_path) as conn:
                value_blob = pickle.dumps(value)
                conn.execute(
                    '''INSERT OR REPLACE INTO cache_entries 
                       (key, value, category, created_at, last_accessed, access_count, ttl)
                       VALUES (?, ?, ?, ?, ?, ?, ?)''',
                    (key, value_blob, category, entry.created_at.isoformat(),
                     entry.last_accessed.isoformat(), entry.access_count, ttl)
                )
                conn.commit()
                
        except Exception as e:
            self.logger.warning(f"Failed to set cache entry {key}: {e}")
    
    def delete(self, key: str):
        """删除缓存条目"""
        if not self.enabled:
            return
        
        # 从内存缓存删除
        if key in self.memory_cache:
            del self.memory_cache[key]
        
        # 从数据库删除
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('DELETE FROM cache_entries WHERE key = ?', (key,))
                conn.commit()
                
        except Exception as e:
            self.logger.warning(f"Failed to delete cache entry {key}: {e}")
    
    def clear_category(self, category: str):
        """清空指定类别的缓存"""
        if not self.enabled:
            return
        
        # 从内存缓存删除
        keys_to_delete = [key for key, entry in self.memory_cache.items() 
                         if entry.category == category]
        for key in keys_to_delete:
            del self.memory_cache[key]
        
        # 从数据库删除
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('DELETE FROM cache_entries WHERE category = ?', (category,))
                conn.commit()
                
        except Exception as e:
            self.logger.warning(f"Failed to clear category {category}: {e}")
    
    def clear_expired(self):
        """清理过期的缓存条目"""
        if not self.enabled:
            return
        
        current_time = datetime.now()
        
        # 清理内存缓存
        expired_keys = [key for key, entry in self.memory_cache.items() 
                       if entry.is_expired()]
        for key in expired_keys:
            del self.memory_cache[key]
        
        # 清理数据库缓存
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 查找过期条目
                cursor = conn.execute(
                    'SELECT key, created_at, ttl FROM cache_entries'
                )
                
                expired_keys = []
                for key, created_str, ttl in cursor.fetchall():
                    created_at = datetime.fromisoformat(created_str)
                    if current_time > created_at + timedelta(seconds=ttl):
                        expired_keys.append(key)
                
                # 删除过期条目
                for key in expired_keys:
                    conn.execute('DELETE FROM cache_entries WHERE key = ?', (key,))
                
                conn.commit()
                
                if expired_keys:
                    self.logger.info(f"Cleared {len(expired_keys)} expired cache entries")
                
        except Exception as e:
            self.logger.warning(f"Failed to clear expired entries: {e}")
    
    def get_cache_key(self, content: str, context_type: str = '') -> str:
        """
        生成缓存键
        
        Args:
            content: 内容
            context_type: 上下文类型
            
        Returns:
            缓存键
        """
        # 使用内容哈希和类型生成键
        content_hash = hashlib.md5(content.encode()).hexdigest()
        return f"{context_type}:{content_hash}"
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        if not self.enabled:
            return {'enabled': False}
        
        stats = {
            'enabled': True,
            'memory_cache_size': len(self.memory_cache),
            'memory_cache_limit': self.max_cache_size,
            'categories': {}
        }
        
        # 内存缓存统计
        for entry in self.memory_cache.values():
            cat = entry.category
            if cat not in stats['categories']:
                stats['categories'][cat] = {'memory': 0, 'database': 0}
            stats['categories'][cat]['memory'] += 1
        
        # 数据库缓存统计
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    'SELECT category, COUNT(*) FROM cache_entries GROUP BY category'
                )
                
                for category, count in cursor.fetchall():
                    if category not in stats['categories']:
                        stats['categories'][category] = {'memory': 0, 'database': 0}
                    stats['categories'][category]['database'] = count
                
                # 总数统计
                cursor = conn.execute('SELECT COUNT(*) FROM cache_entries')
                stats['database_total'] = cursor.fetchone()[0]
                
        except Exception as e:
            self.logger.warning(f"Failed to get database statistics: {e}")
            stats['database_total'] = 0
        
        return stats
    
    def optimize_cache(self):
        """优化缓存性能"""
        if not self.enabled:
            return
        
        # 清理过期条目
        self.clear_expired()
        
        # 如果内存缓存过大，移除最少使用的条目
        if len(self.memory_cache) > self.max_cache_size:
            # 按访问次数和最后访问时间排序
            sorted_entries = sorted(
                self.memory_cache.items(),
                key=lambda x: (x[1].access_count, x[1].last_accessed)
            )
            
            # 移除最少使用的条目
            remove_count = len(self.memory_cache) - self.max_cache_size + 100
            for i in range(remove_count):
                if i < len(sorted_entries):
                    key = sorted_entries[i][0]
                    del self.memory_cache[key]
        
        # 数据库优化
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 清理访问次数为0且超过1小时的条目
                cutoff_time = datetime.now() - timedelta(hours=1)
                conn.execute(
                    'DELETE FROM cache_entries WHERE access_count = 0 AND created_at < ?',
                    (cutoff_time.isoformat(),)
                )
                
                # 压缩数据库
                conn.execute('VACUUM')
                conn.commit()
                
        except Exception as e:
            self.logger.warning(f"Failed to optimize database: {e}")
        
        self.logger.info("Cache optimization completed")


# 全局缓存管理器实例
_cache_manager: Optional[LayeredCacheManager] = None


def get_cache_manager(config: Optional[Dict[str, Any]] = None) -> LayeredCacheManager:
    """
    获取全局缓存管理器实例
    
    Args:
        config: 配置字典
        
    Returns:
        LayeredCacheManager实例
    """
    global _cache_manager
    
    if _cache_manager is None:
        if config is None:
            # 使用默认配置
            config = {
                'augment': {
                    'cache': {
                        'layered_cache_enabled': True,
                        'cache_ttl': 3600,
                        'max_cache_size': 1000,
                        'cache_file': 'data/augment_cache.db'
                    }
                }
            }
        _cache_manager = LayeredCacheManager(config)
    
    return _cache_manager
