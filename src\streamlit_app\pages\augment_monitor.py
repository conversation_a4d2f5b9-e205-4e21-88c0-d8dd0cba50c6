"""
Augment监控仪表板

实时显示token使用情况和系统状态，提供配置参数调整界面。
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from datetime import datetime, timedelta
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

try:
    from src.core.config.augment_config import get_augment_config, AugmentConfigLoader
    from src.core.token_manager import TokenBudgetManager, TokenUsageStats, ContextItem
except ImportError as e:
    st.error(f"导入模块失败: {e}")
    st.stop()


def init_session_state():
    """初始化会话状态"""
    if 'token_manager' not in st.session_state:
        try:
            config_loader = AugmentConfigLoader()
            config_data = config_loader.load_config()
            st.session_state.token_manager = TokenBudgetManager(config_data)
        except Exception as e:
            st.error(f"初始化Token管理器失败: {e}")
            st.session_state.token_manager = None
    
    if 'last_update' not in st.session_state:
        st.session_state.last_update = datetime.now()
    
    if 'demo_context_items' not in st.session_state:
        # 创建演示数据
        st.session_state.demo_context_items = [
            ContextItem("class TokenBudgetManager:", 0.9, "core", 0.9, 1200),
            ContextItem("def smart_truncate(self, context_items):", 0.8, "core", 0.8, 800),
            ContextItem("import tiktoken", 0.7, "related", 0.7, 50),
            ContextItem("# Configuration settings", 0.3, "background", 0.3, 100),
            ContextItem("def calculate_importance_score(self):", 0.6, "related", 0.6, 600),
        ]


def display_header():
    """显示页面标题"""
    st.title("🔧 Augment Token监控仪表板")
    st.markdown("---")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("系统状态", "运行中", "正常")
    
    with col2:
        current_time = datetime.now().strftime("%H:%M:%S")
        st.metric("当前时间", current_time)
    
    with col3:
        if st.button("🔄 刷新数据"):
            st.session_state.last_update = datetime.now()
            st.rerun()


def display_token_usage():
    """显示Token使用情况"""
    st.subheader("📊 Token使用情况")
    
    if st.session_state.token_manager is None:
        st.error("Token管理器未初始化")
        return
    
    token_manager = st.session_state.token_manager
    
    # 模拟当前使用情况
    demo_items = st.session_state.demo_context_items
    total_tokens = sum(item.token_count for item in demo_items)
    
    # 更新统计
    token_manager.update_usage_stats(demo_items, response_time=1.2)
    stats = token_manager.get_usage_stats()
    
    # 显示主要指标
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        usage_ratio = total_tokens / token_manager.max_tokens
        st.metric(
            "Token使用率", 
            f"{usage_ratio:.1%}",
            f"{total_tokens:,} / {token_manager.max_tokens:,}"
        )
    
    with col2:
        core_tokens = sum(item.token_count for item in demo_items if item.category == 'core')
        st.metric("核心上下文", f"{core_tokens:,}", "tokens")
    
    with col3:
        related_tokens = sum(item.token_count for item in demo_items if item.category == 'related')
        st.metric("相关上下文", f"{related_tokens:,}", "tokens")
    
    with col4:
        bg_tokens = sum(item.token_count for item in demo_items if item.category == 'background')
        st.metric("背景上下文", f"{bg_tokens:,}", "tokens")
    
    # Token使用进度条
    progress_color = "normal"
    if usage_ratio >= token_manager.critical_threshold:
        progress_color = "red"
    elif usage_ratio >= token_manager.warning_threshold:
        progress_color = "orange"
    else:
        progress_color = "green"
    
    st.progress(usage_ratio)
    
    # 预警信息
    if token_manager.should_trigger_warning(total_tokens):
        warning_msg = token_manager.get_warning_message(total_tokens)
        if usage_ratio >= token_manager.critical_threshold:
            st.error(f"🚨 {warning_msg}")
        else:
            st.warning(f"⚠️ {warning_msg}")


def display_context_breakdown():
    """显示上下文分解"""
    st.subheader("📋 上下文分解")
    
    demo_items = st.session_state.demo_context_items
    
    # 创建数据框
    df = pd.DataFrame([
        {
            "内容": item.content[:50] + "..." if len(item.content) > 50 else item.content,
            "类别": item.category,
            "Token数量": item.token_count,
            "重要性": f"{item.importance_score:.2f}",
            "优先级": f"{item.priority:.2f}"
        }
        for item in demo_items
    ])
    
    # 显示表格
    st.dataframe(df, use_container_width=True)
    
    # 饼图显示分布
    col1, col2 = st.columns(2)
    
    with col1:
        category_counts = df.groupby('类别')['Token数量'].sum().reset_index()
        fig_pie = px.pie(
            category_counts, 
            values='Token数量', 
            names='类别',
            title="Token分布（按类别）",
            color_discrete_map={
                'core': '#FF6B6B',
                'related': '#4ECDC4', 
                'background': '#45B7D1'
            }
        )
        st.plotly_chart(fig_pie, use_container_width=True)
    
    with col2:
        # 重要性分布
        fig_bar = px.bar(
            df, 
            x='内容', 
            y='Token数量',
            color='类别',
            title="Token使用量（按内容）",
            color_discrete_map={
                'core': '#FF6B6B',
                'related': '#4ECDC4', 
                'background': '#45B7D1'
            }
        )
        fig_bar.update_xaxis(tickangle=45)
        st.plotly_chart(fig_bar, use_container_width=True)


def display_performance_metrics():
    """显示性能指标"""
    st.subheader("⚡ 性能指标")
    
    if st.session_state.token_manager is None:
        return
    
    stats = st.session_state.token_manager.get_usage_stats()
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        response_time = stats.get('current', {}).get('response_time', 0)
        st.metric("响应时间", f"{response_time:.2f}s", "当前")
    
    with col2:
        avg_response = stats.get('recent_average', {}).get('response_time', 0)
        st.metric("平均响应时间", f"{avg_response:.2f}s", "最近10次")
    
    with col3:
        history_count = stats.get('history_count', 0)
        st.metric("历史记录", f"{history_count}", "条")
    
    # 模拟历史数据图表
    if history_count > 0:
        # 生成模拟时间序列数据
        dates = [datetime.now() - timedelta(hours=i) for i in range(24, 0, -1)]
        token_usage = [150000 + (i * 1000) + (i % 3 * 5000) for i in range(24)]
        response_times = [1.0 + (i * 0.1) + (i % 4 * 0.2) for i in range(24)]
        
        df_history = pd.DataFrame({
            '时间': dates,
            'Token使用量': token_usage,
            '响应时间': response_times
        })
        
        # Token使用趋势
        fig_trend = go.Figure()
        fig_trend.add_trace(go.Scatter(
            x=df_history['时间'],
            y=df_history['Token使用量'],
            mode='lines+markers',
            name='Token使用量',
            line=dict(color='#FF6B6B')
        ))
        
        # 添加阈值线
        max_tokens = st.session_state.token_manager.max_tokens
        fig_trend.add_hline(
            y=max_tokens * 0.8, 
            line_dash="dash", 
            line_color="orange",
            annotation_text="预警阈值"
        )
        fig_trend.add_hline(
            y=max_tokens * 0.95, 
            line_dash="dash", 
            line_color="red",
            annotation_text="紧急阈值"
        )
        
        fig_trend.update_layout(
            title="Token使用趋势（24小时）",
            xaxis_title="时间",
            yaxis_title="Token数量"
        )
        
        st.plotly_chart(fig_trend, use_container_width=True)


def display_configuration():
    """显示配置设置"""
    st.subheader("⚙️ 配置设置")
    
    if st.session_state.token_manager is None:
        return
    
    token_manager = st.session_state.token_manager
    
    with st.expander("Token限制设置", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            max_tokens = st.number_input(
                "最大Token数量",
                min_value=50000,
                max_value=500000,
                value=token_manager.max_tokens,
                step=10000
            )
        
        with col2:
            warning_threshold = st.slider(
                "预警阈值",
                min_value=0.5,
                max_value=1.0,
                value=token_manager.warning_threshold,
                step=0.05
            )
    
    with st.expander("上下文分层设置", expanded=False):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            core_priority = st.slider(
                "核心优先级",
                min_value=0.0,
                max_value=1.0,
                value=token_manager.core_priority,
                step=0.1
            )
        
        with col2:
            related_priority = st.slider(
                "相关优先级",
                min_value=0.0,
                max_value=1.0,
                value=token_manager.related_priority,
                step=0.1
            )
        
        with col3:
            background_priority = st.slider(
                "背景优先级",
                min_value=0.0,
                max_value=1.0,
                value=token_manager.background_priority,
                step=0.1
            )
    
    if st.button("💾 保存配置"):
        st.success("配置已保存（演示模式）")


def display_smart_truncation_demo():
    """显示智能截断演示"""
    st.subheader("✂️ 智能截断演示")
    
    if st.session_state.token_manager is None:
        return
    
    token_manager = st.session_state.token_manager
    demo_items = st.session_state.demo_context_items.copy()
    
    # 设置目标token数量
    target_tokens = st.slider(
        "目标Token数量",
        min_value=500,
        max_value=3000,
        value=2000,
        step=100
    )
    
    if st.button("🔄 执行智能截断"):
        # 计算重要性评分
        for item in demo_items:
            item.importance_score = token_manager.calculate_importance_score(
                item.content, item.category
            )
        
        # 执行智能截断
        truncated_items = token_manager.smart_truncate(demo_items, target_tokens)
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**截断前:**")
            original_tokens = sum(item.token_count for item in demo_items)
            st.write(f"总Token数: {original_tokens}")
            st.write(f"项目数: {len(demo_items)}")
            
            for item in demo_items:
                st.write(f"- {item.content[:30]}... ({item.token_count} tokens)")
        
        with col2:
            st.write("**截断后:**")
            truncated_tokens = sum(item.token_count for item in truncated_items)
            st.write(f"总Token数: {truncated_tokens}")
            st.write(f"项目数: {len(truncated_items)}")
            
            for item in truncated_items:
                st.write(f"- {item.content[:30]}... ({item.token_count} tokens)")
        
        # 显示截断效果
        reduction_ratio = (original_tokens - truncated_tokens) / original_tokens
        st.success(f"截断效果: 减少了 {reduction_ratio:.1%} 的Token使用量")


def main():
    """主函数"""
    st.set_page_config(
        page_title="Augment监控",
        page_icon="🔧",
        layout="wide"
    )
    
    # 初始化
    init_session_state()
    
    # 显示页面内容
    display_header()
    
    # 主要内容区域
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📊 Token使用", 
        "📋 上下文分解", 
        "⚡ 性能指标", 
        "⚙️ 配置设置",
        "✂️ 智能截断"
    ])
    
    with tab1:
        display_token_usage()
    
    with tab2:
        display_context_breakdown()
    
    with tab3:
        display_performance_metrics()
    
    with tab4:
        display_configuration()
    
    with tab5:
        display_smart_truncation_demo()
    
    # 页脚
    st.markdown("---")
    st.markdown("*Augment Token监控仪表板 - 实时监控和优化Token使用*")


if __name__ == "__main__":
    main()
