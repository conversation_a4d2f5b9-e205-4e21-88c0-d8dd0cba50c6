"""
分层上下文管理器

实现三层上下文管理架构：核心层、相关层、背景层，支持动态优先级调整和智能缓存。
"""

import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import hashlib
import pickle
import os


class ContextCategory(Enum):
    """上下文类别枚举"""
    CORE = "core"
    RELATED = "related"
    BACKGROUND = "background"


@dataclass
class ContextLayer:
    """上下文层"""
    category: ContextCategory
    priority: float
    max_tokens: int
    items: List['ContextItem'] = field(default_factory=list)
    
    def add_item(self, item: 'ContextItem'):
        """添加上下文项目"""
        item.category = self.category.value
        self.items.append(item)
    
    def remove_item(self, item: 'ContextItem'):
        """移除上下文项目"""
        if item in self.items:
            self.items.remove(item)
    
    def get_total_tokens(self) -> int:
        """获取总token数量"""
        return sum(item.token_count for item in self.items)
    
    def sort_by_importance(self):
        """按重要性排序"""
        self.items.sort(key=lambda x: x.importance_score, reverse=True)


@dataclass
class ContextItem:
    """上下文项目"""
    content: str
    priority: float
    category: str = "background"
    importance_score: float = 0.0
    token_count: int = 0
    file_path: Optional[str] = None
    line_range: Optional[Tuple[int, int]] = None
    dependencies: Set[str] = field(default_factory=set)
    last_accessed: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    
    def __post_init__(self):
        """初始化后处理"""
        if self.token_count == 0:
            # 简单的token计数估算
            self.token_count = len(self.content) // 4
    
    def update_access(self):
        """更新访问信息"""
        self.last_accessed = datetime.now()
        self.access_count += 1
    
    def get_content_hash(self) -> str:
        """获取内容哈希"""
        return hashlib.md5(self.content.encode()).hexdigest()


class ContextManager:
    """分层上下文管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化上下文管理器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        self.context_config = self.config.get('context_management', {})
        self.cache_config = self.config.get('cache', {})
        
        # 初始化层配置
        layer_config = self.context_config.get('context_layers', {})
        self.core_priority = layer_config.get('core_priority', 0.6)
        self.related_priority = layer_config.get('related_priority', 0.3)
        self.background_priority = layer_config.get('background_priority', 0.1)
        
        # Token限制
        self.max_tokens = self.context_config.get('max_tokens', 180000)
        
        # 动态调整配置
        dynamic_config = self.context_config.get('dynamic_adjustment', {})
        self.dynamic_enabled = dynamic_config.get('enabled', True)
        self.adjustment_factor = dynamic_config.get('adjustment_factor', 0.1)
        self.min_core_ratio = dynamic_config.get('min_core_ratio', 0.4)
        self.max_core_ratio = dynamic_config.get('max_core_ratio', 0.8)
        
        # 初始化层
        self.layers = {
            ContextCategory.CORE: ContextLayer(
                ContextCategory.CORE, 
                self.core_priority,
                int(self.max_tokens * self.core_priority)
            ),
            ContextCategory.RELATED: ContextLayer(
                ContextCategory.RELATED,
                self.related_priority,
                int(self.max_tokens * self.related_priority)
            ),
            ContextCategory.BACKGROUND: ContextLayer(
                ContextCategory.BACKGROUND,
                self.background_priority,
                int(self.max_tokens * self.background_priority)
            )
        }
        
        # 缓存管理
        self.cache_enabled = self.cache_config.get('layered_cache_enabled', True)
        self.cache_ttl = self.cache_config.get('cache_ttl', 3600)
        self.cache_file = self.cache_config.get('cache_file', 'data/context_cache.pkl')
        self._context_cache: Dict[str, Any] = {}
        
        # 依赖关系图
        self.dependency_graph: Dict[str, Set[str]] = {}
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 加载缓存
        self._load_cache()
    
    def add_context_item(self, item: ContextItem, category: Optional[ContextCategory] = None):
        """
        添加上下文项目
        
        Args:
            item: 上下文项目
            category: 指定类别，如果为None则自动分类
        """
        if category is None:
            category = self._classify_context_item(item)
        
        # 更新项目类别
        item.category = category.value
        
        # 添加到对应层
        self.layers[category].add_item(item)
        
        # 更新依赖关系
        if item.file_path:
            self._update_dependencies(item)
        
        # 更新访问信息
        item.update_access()
        
        self.logger.debug(f"Added context item to {category.value} layer: {item.content[:50]}...")
    
    def remove_context_item(self, item: ContextItem):
        """移除上下文项目"""
        for layer in self.layers.values():
            layer.remove_item(item)
    
    def get_optimized_context(self, target_tokens: Optional[int] = None) -> List[ContextItem]:
        """
        获取优化后的上下文
        
        Args:
            target_tokens: 目标token数量，如果为None则使用配置的最大值
            
        Returns:
            优化后的上下文项目列表
        """
        if target_tokens is None:
            target_tokens = self.max_tokens
        
        # 动态调整层优先级
        if self.dynamic_enabled:
            self._adjust_layer_priorities()
        
        # 按层收集上下文
        result = []
        remaining_tokens = target_tokens
        
        # 按优先级顺序处理层
        for category in [ContextCategory.CORE, ContextCategory.RELATED, ContextCategory.BACKGROUND]:
            layer = self.layers[category]
            layer.sort_by_importance()
            
            # 计算该层可用的token数量
            layer_tokens = min(remaining_tokens, int(target_tokens * layer.priority))
            
            # 选择该层的项目
            current_tokens = 0
            for item in layer.items:
                if current_tokens + item.token_count <= layer_tokens:
                    result.append(item)
                    current_tokens += item.token_count
                    item.update_access()
            
            remaining_tokens -= current_tokens
            
            if remaining_tokens <= 0:
                break
        
        self.logger.info(f"Optimized context: {len(result)} items, "
                        f"{sum(item.token_count for item in result)} tokens")
        
        return result
    
    def _classify_context_item(self, item: ContextItem) -> ContextCategory:
        """
        自动分类上下文项目
        
        Args:
            item: 上下文项目
            
        Returns:
            分类结果
        """
        content = item.content.lower()
        
        # 核心代码特征
        core_keywords = [
            'class ', 'def ', 'function', 'method', 'api', 'endpoint',
            'main', 'init', 'error', 'exception', 'critical'
        ]
        
        # 相关代码特征
        related_keywords = [
            'import', 'from', 'config', 'setting', 'util', 'helper',
            'test', 'mock', 'fixture'
        ]
        
        # 检查核心特征
        core_score = sum(1 for keyword in core_keywords if keyword in content)
        related_score = sum(1 for keyword in related_keywords if keyword in content)
        
        # 基于文件路径的分类
        if item.file_path:
            path_lower = item.file_path.lower()
            if any(pattern in path_lower for pattern in ['core', 'main', 'api', 'model']):
                core_score += 2
            elif any(pattern in path_lower for pattern in ['util', 'helper', 'config', 'test']):
                related_score += 1
        
        # 基于重要性评分的分类
        if item.importance_score > 0.7:
            core_score += 1
        elif item.importance_score > 0.4:
            related_score += 1
        
        # 决定分类
        if core_score >= 2:
            return ContextCategory.CORE
        elif related_score >= 1 or core_score >= 1:
            return ContextCategory.RELATED
        else:
            return ContextCategory.BACKGROUND
    
    def _adjust_layer_priorities(self):
        """动态调整层优先级"""
        # 计算当前使用情况
        total_items = sum(len(layer.items) for layer in self.layers.values())
        if total_items == 0:
            return
        
        core_ratio = len(self.layers[ContextCategory.CORE].items) / total_items
        
        # 根据核心内容比例调整
        if core_ratio > 0.7:  # 核心内容过多，增加核心层优先级
            adjustment = min(self.adjustment_factor, self.max_core_ratio - self.core_priority)
            self.core_priority = min(self.max_core_ratio, self.core_priority + adjustment)
        elif core_ratio < 0.3:  # 核心内容过少，减少核心层优先级
            adjustment = min(self.adjustment_factor, self.core_priority - self.min_core_ratio)
            self.core_priority = max(self.min_core_ratio, self.core_priority - adjustment)
        
        # 重新分配其他层的优先级
        remaining = 1.0 - self.core_priority
        self.related_priority = remaining * 0.7  # 相关层占剩余的70%
        self.background_priority = remaining * 0.3  # 背景层占剩余的30%
        
        # 更新层配置
        self.layers[ContextCategory.CORE].priority = self.core_priority
        self.layers[ContextCategory.RELATED].priority = self.related_priority
        self.layers[ContextCategory.BACKGROUND].priority = self.background_priority
        
        self.logger.debug(f"Adjusted priorities: core={self.core_priority:.2f}, "
                         f"related={self.related_priority:.2f}, "
                         f"background={self.background_priority:.2f}")
    
    def _update_dependencies(self, item: ContextItem):
        """更新依赖关系图"""
        if not item.file_path:
            return
        
        # 简单的依赖分析（基于import语句）
        import_lines = [line.strip() for line in item.content.split('\n') 
                       if line.strip().startswith(('import ', 'from '))]
        
        dependencies = set()
        for line in import_lines:
            if line.startswith('from '):
                # from module import ...
                module = line.split()[1]
                dependencies.add(module)
            elif line.startswith('import '):
                # import module
                modules = line.replace('import ', '').split(',')
                for module in modules:
                    dependencies.add(module.strip())
        
        self.dependency_graph[item.file_path] = dependencies
        item.dependencies = dependencies
    
    def get_related_items(self, item: ContextItem) -> List[ContextItem]:
        """获取相关的上下文项目"""
        related = []
        
        # 基于依赖关系查找
        if item.file_path and item.file_path in self.dependency_graph:
            dependencies = self.dependency_graph[item.file_path]
            
            for layer in self.layers.values():
                for other_item in layer.items:
                    if other_item.file_path and other_item.file_path != item.file_path:
                        # 检查是否有依赖关系
                        if (other_item.file_path in dependencies or
                            any(dep in other_item.content for dep in dependencies)):
                            related.append(other_item)
        
        return related
    
    def _load_cache(self):
        """加载缓存"""
        if not self.cache_enabled:
            return
        
        cache_path = self.cache_file
        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    self._context_cache = pickle.load(f)
                self.logger.debug(f"Loaded context cache from {cache_path}")
            except Exception as e:
                self.logger.warning(f"Failed to load context cache: {e}")
                self._context_cache = {}
    
    def _save_cache(self):
        """保存缓存"""
        if not self.cache_enabled:
            return
        
        cache_path = self.cache_file
        os.makedirs(os.path.dirname(cache_path), exist_ok=True)
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(self._context_cache, f)
            self.logger.debug(f"Saved context cache to {cache_path}")
        except Exception as e:
            self.logger.warning(f"Failed to save context cache: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {}
        
        for category, layer in self.layers.items():
            stats[category.value] = {
                'item_count': len(layer.items),
                'total_tokens': layer.get_total_tokens(),
                'priority': layer.priority,
                'max_tokens': layer.max_tokens
            }
        
        stats['total'] = {
            'item_count': sum(len(layer.items) for layer in self.layers.values()),
            'total_tokens': sum(layer.get_total_tokens() for layer in self.layers.values()),
            'dependency_count': len(self.dependency_graph)
        }
        
        return stats
    
    def clear_expired_items(self, max_age_hours: int = 24):
        """清理过期的上下文项目"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        for layer in self.layers.values():
            expired_items = [item for item in layer.items 
                           if item.last_accessed < cutoff_time and item.access_count < 2]
            
            for item in expired_items:
                layer.remove_item(item)
        
        self.logger.info(f"Cleared expired context items older than {max_age_hours} hours")
    
    def __del__(self):
        """析构函数，保存缓存"""
        self._save_cache()
