"""
代码语义压缩引擎

实现代码语义压缩和可逆解压缩，大幅减少token使用量同时保留关键信息。
"""

import logging
import json
import re
import ast
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib

# 尝试导入AST分析器
try:
    from ..tools.ast_analyzer import ASTAnalyzer, CodeStructure, NodeType
except ImportError:
    # 如果导入失败，使用简化版本
    ASTAnalyzer = None
    CodeStructure = None
    NodeType = None


class CompressionLevel(Enum):
    """压缩级别"""
    LIGHT = "light"      # 轻度压缩，保留大部分信息
    MEDIUM = "medium"    # 中度压缩，保留关键信息
    HEAVY = "heavy"      # 重度压缩，只保留核心结构


@dataclass
class CompressionResult:
    """压缩结果"""
    compressed_content: str
    original_size: int
    compressed_size: int
    compression_ratio: float
    metadata: Dict[str, Any]
    reversible: bool = True
    
    @property
    def size_reduction(self) -> int:
        """获取大小减少量"""
        return self.original_size - self.compressed_size
    
    @property
    def reduction_percentage(self) -> float:
        """获取减少百分比"""
        return (self.size_reduction / self.original_size) * 100 if self.original_size > 0 else 0


@dataclass
class SemanticElement:
    """语义元素"""
    element_type: str
    name: str
    signature: str
    importance: float
    dependencies: Set[str]
    content_hash: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'type': self.element_type,
            'name': self.name,
            'signature': self.signature,
            'importance': self.importance,
            'dependencies': list(self.dependencies),
            'hash': self.content_hash
        }


class SemanticAnalyzer:
    """语义分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = logging.getLogger(__name__)
        self.ast_analyzer = ASTAnalyzer() if ASTAnalyzer else None
    
    def analyze_semantics(self, code: str) -> List[SemanticElement]:
        """
        分析代码语义
        
        Args:
            code: 代码内容
            
        Returns:
            语义元素列表
        """
        elements = []
        
        if self.ast_analyzer:
            # 使用AST分析器
            analysis_result = self.ast_analyzer.analyze_code(code)
            if 'structures' in analysis_result:
                for structure in analysis_result['structures']:
                    element = self._structure_to_element(structure, code)
                    elements.append(element)
        else:
            # 使用正则表达式回退方案
            elements = self._regex_analysis(code)
        
        return elements
    
    def _structure_to_element(self, structure: 'CodeStructure', code: str) -> SemanticElement:
        """将代码结构转换为语义元素"""
        lines = code.split('\n')
        content = '\n'.join(lines[structure.line_start-1:structure.line_end])
        
        return SemanticElement(
            element_type=structure.node_type.value,
            name=structure.name,
            signature=self._extract_signature(structure, content),
            importance=self._calculate_semantic_importance(structure),
            dependencies=structure.dependencies,
            content_hash=hashlib.md5(content.encode()).hexdigest()
        )
    
    def _regex_analysis(self, code: str) -> List[SemanticElement]:
        """正则表达式分析回退方案"""
        elements = []
        lines = code.split('\n')
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # 类定义
            class_match = re.match(r'^\s*class\s+(\w+)', line)
            if class_match:
                elements.append(SemanticElement(
                    element_type='class',
                    name=class_match.group(1),
                    signature=line_stripped,
                    importance=0.9,
                    dependencies=set(),
                    content_hash=hashlib.md5(line.encode()).hexdigest()
                ))
            
            # 函数定义
            func_match = re.match(r'^\s*def\s+(\w+)', line)
            if func_match:
                elements.append(SemanticElement(
                    element_type='function',
                    name=func_match.group(1),
                    signature=line_stripped,
                    importance=0.7,
                    dependencies=set(),
                    content_hash=hashlib.md5(line.encode()).hexdigest()
                ))
            
            # 导入语句
            import_match = re.match(r'^\s*(import|from)\s+(\w+)', line)
            if import_match:
                elements.append(SemanticElement(
                    element_type='import',
                    name=import_match.group(2),
                    signature=line_stripped,
                    importance=0.5,
                    dependencies=set(),
                    content_hash=hashlib.md5(line.encode()).hexdigest()
                ))
        
        return elements
    
    def _extract_signature(self, structure: 'CodeStructure', content: str) -> str:
        """提取签名"""
        lines = content.split('\n')
        if lines:
            # 返回第一行作为签名
            return lines[0].strip()
        return ""
    
    def _calculate_semantic_importance(self, structure: 'CodeStructure') -> float:
        """计算语义重要性"""
        importance = 0.5  # 基础重要性
        
        if structure.node_type.value == 'class':
            importance = 0.9
        elif structure.node_type.value == 'function':
            importance = 0.7
        elif structure.node_type.value == 'method':
            importance = 0.6
        elif structure.node_type.value == 'import':
            importance = 0.4
        
        # 公共成员加分
        if structure.is_public:
            importance += 0.1
        
        # 复杂度加分
        if structure.complexity > 5:
            importance += 0.1
        
        # 有文档字符串加分
        if structure.docstring:
            importance += 0.1
        
        return min(importance, 1.0)


class CodeCompressor:
    """代码压缩器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化压缩器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        optimization_config = self.config.get('optimization', {})
        
        self.compression_enabled = optimization_config.get('compression_enabled', False)
        self.compression_ratio = optimization_config.get('compression_ratio', 0.7)
        
        self.semantic_analyzer = SemanticAnalyzer()
        self.logger = logging.getLogger(__name__)
        
        # 压缩模板
        self.compression_templates = {
            'class': "class {name}({bases}): # {methods_count} methods, {complexity} complexity",
            'function': "def {name}({params}) -> {return_type}: # {complexity} complexity",
            'method': "def {name}({params}): # method of {parent}",
            'import': "import {name}  # {usage_count} usages"
        }
    
    def compress_code(self, code: str, level: CompressionLevel = CompressionLevel.MEDIUM) -> CompressionResult:
        """
        压缩代码
        
        Args:
            code: 原始代码
            level: 压缩级别
            
        Returns:
            压缩结果
        """
        if not self.compression_enabled:
            return CompressionResult(
                compressed_content=code,
                original_size=len(code),
                compressed_size=len(code),
                compression_ratio=1.0,
                metadata={'compression_disabled': True},
                reversible=False
            )
        
        original_size = len(code)
        
        try:
            # 分析语义
            semantic_elements = self.semantic_analyzer.analyze_semantics(code)
            
            # 根据级别压缩
            if level == CompressionLevel.LIGHT:
                compressed_content = self._light_compression(code, semantic_elements)
            elif level == CompressionLevel.MEDIUM:
                compressed_content = self._medium_compression(code, semantic_elements)
            else:  # HEAVY
                compressed_content = self._heavy_compression(code, semantic_elements)
            
            compressed_size = len(compressed_content)
            compression_ratio = compressed_size / original_size if original_size > 0 else 1.0
            
            # 生成元数据
            metadata = {
                'compression_level': level.value,
                'semantic_elements_count': len(semantic_elements),
                'elements': [element.to_dict() for element in semantic_elements],
                'original_hash': hashlib.md5(code.encode()).hexdigest()
            }
            
            return CompressionResult(
                compressed_content=compressed_content,
                original_size=original_size,
                compressed_size=compressed_size,
                compression_ratio=compression_ratio,
                metadata=metadata,
                reversible=True
            )
            
        except Exception as e:
            self.logger.error(f"Compression failed: {e}")
            return CompressionResult(
                compressed_content=code,
                original_size=original_size,
                compressed_size=original_size,
                compression_ratio=1.0,
                metadata={'error': str(e)},
                reversible=False
            )
    
    def _light_compression(self, code: str, elements: List[SemanticElement]) -> str:
        """轻度压缩：主要移除注释和空行"""
        lines = code.split('\n')
        compressed_lines = []
        
        for line in lines:
            stripped = line.strip()
            
            # 跳过空行
            if not stripped:
                continue
            
            # 跳过单行注释
            if stripped.startswith('#'):
                continue
            
            # 移除行尾注释
            if '#' in line:
                code_part = line.split('#')[0].rstrip()
                if code_part:
                    compressed_lines.append(code_part)
            else:
                compressed_lines.append(line)
        
        return '\n'.join(compressed_lines)
    
    def _medium_compression(self, code: str, elements: List[SemanticElement]) -> str:
        """中度压缩：保留关键结构，压缩实现细节"""
        compressed_parts = []
        
        # 按重要性排序
        elements.sort(key=lambda x: x.importance, reverse=True)
        
        # 保留重要元素的签名
        for element in elements:
            if element.importance >= 0.6:  # 只保留重要元素
                template = self.compression_templates.get(element.element_type, "{signature}")
                
                if element.element_type == 'class':
                    compressed_signature = template.format(
                        name=element.name,
                        bases="",
                        methods_count=0,  # 简化
                        complexity=0
                    )
                elif element.element_type in ['function', 'method']:
                    compressed_signature = template.format(
                        name=element.name,
                        params="...",
                        return_type="Any",
                        complexity=0,
                        parent=getattr(element, 'parent', '')
                    )
                else:
                    compressed_signature = element.signature
                
                compressed_parts.append(compressed_signature)
        
        return '\n'.join(compressed_parts)
    
    def _heavy_compression(self, code: str, elements: List[SemanticElement]) -> str:
        """重度压缩：只保留最核心的结构信息"""
        core_elements = [e for e in elements if e.importance >= 0.8]
        
        if not core_elements:
            # 如果没有核心元素，至少保留一些基本信息
            return f"# Code summary: {len(elements)} elements"
        
        summary_parts = []
        
        # 统计信息
        element_counts = {}
        for element in elements:
            element_counts[element.element_type] = element_counts.get(element.element_type, 0) + 1
        
        summary_parts.append(f"# Code structure: {dict(element_counts)}")
        
        # 核心元素
        for element in core_elements:
            summary_parts.append(f"# {element.element_type}: {element.name}")
        
        return '\n'.join(summary_parts)
    
    def decompress_code(self, compressed_result: CompressionResult) -> str:
        """
        解压缩代码
        
        Args:
            compressed_result: 压缩结果
            
        Returns:
            解压缩后的代码
        """
        if not compressed_result.reversible:
            self.logger.warning("Code is not reversible")
            return compressed_result.compressed_content
        
        try:
            metadata = compressed_result.metadata
            level = CompressionLevel(metadata.get('compression_level', 'medium'))
            
            if level == CompressionLevel.LIGHT:
                return self._decompress_light(compressed_result)
            elif level == CompressionLevel.MEDIUM:
                return self._decompress_medium(compressed_result)
            else:  # HEAVY
                return self._decompress_heavy(compressed_result)
                
        except Exception as e:
            self.logger.error(f"Decompression failed: {e}")
            return compressed_result.compressed_content
    
    def _decompress_light(self, result: CompressionResult) -> str:
        """轻度解压缩"""
        # 轻度压缩主要是移除注释，无法完全恢复
        return result.compressed_content + "\n# Note: Comments were removed during compression"
    
    def _decompress_medium(self, result: CompressionResult) -> str:
        """中度解压缩"""
        elements = result.metadata.get('elements', [])
        
        reconstructed_parts = []
        reconstructed_parts.append("# Reconstructed from compressed code")
        
        for element_data in elements:
            element_type = element_data.get('type', 'unknown')
            name = element_data.get('name', 'unknown')
            
            if element_type == 'class':
                reconstructed_parts.append(f"class {name}:")
                reconstructed_parts.append("    pass  # Implementation compressed")
            elif element_type in ['function', 'method']:
                reconstructed_parts.append(f"def {name}():")
                reconstructed_parts.append("    pass  # Implementation compressed")
            elif element_type == 'import':
                reconstructed_parts.append(f"import {name}")
        
        return '\n'.join(reconstructed_parts)
    
    def _decompress_heavy(self, result: CompressionResult) -> str:
        """重度解压缩"""
        return result.compressed_content + "\n# Note: Heavy compression - original structure lost"
    
    def get_compression_stats(self, results: List[CompressionResult]) -> Dict[str, Any]:
        """
        获取压缩统计信息
        
        Args:
            results: 压缩结果列表
            
        Returns:
            统计信息
        """
        if not results:
            return {}
        
        total_original = sum(r.original_size for r in results)
        total_compressed = sum(r.compressed_size for r in results)
        
        return {
            'total_files': len(results),
            'total_original_size': total_original,
            'total_compressed_size': total_compressed,
            'total_size_reduction': total_original - total_compressed,
            'average_compression_ratio': sum(r.compression_ratio for r in results) / len(results),
            'best_compression_ratio': min(r.compression_ratio for r in results),
            'worst_compression_ratio': max(r.compression_ratio for r in results),
            'reversible_count': sum(1 for r in results if r.reversible),
            'average_reduction_percentage': sum(r.reduction_percentage for r in results) / len(results)
        }
