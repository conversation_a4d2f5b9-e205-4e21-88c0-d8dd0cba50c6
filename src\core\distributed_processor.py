"""
分布式任务处理器

实现大型任务的智能分解和本地多进程并行处理，突破单次token限制。
"""

import asyncio
import logging
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass
import time
import hashlib
import pickle


@dataclass
class TaskChunk:
    """任务块"""
    chunk_id: str
    content: str
    metadata: Dict[str, Any]
    priority: float = 0.5
    estimated_tokens: int = 0
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.estimated_tokens == 0:
            self.estimated_tokens = len(self.content) // 4


@dataclass
class ProcessingResult:
    """处理结果"""
    chunk_id: str
    result: Any
    processing_time: float
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class TaskDecomposer:
    """任务分解器"""
    
    def __init__(self, max_chunk_tokens: int = 5000):
        """
        初始化任务分解器
        
        Args:
            max_chunk_tokens: 每个块的最大token数
        """
        self.max_chunk_tokens = max_chunk_tokens
        self.logger = logging.getLogger(__name__)
    
    def decompose_by_content(self, content: str, overlap_ratio: float = 0.1) -> List[TaskChunk]:
        """
        按内容大小分解任务
        
        Args:
            content: 内容
            overlap_ratio: 重叠比例
            
        Returns:
            任务块列表
        """
        chunks = []
        lines = content.split('\n')
        
        current_chunk_lines = []
        current_tokens = 0
        chunk_index = 0
        
        overlap_lines = int(len(lines) * overlap_ratio) if overlap_ratio > 0 else 0
        
        for i, line in enumerate(lines):
            line_tokens = len(line) // 4
            
            if current_tokens + line_tokens > self.max_chunk_tokens and current_chunk_lines:
                # 创建当前块
                chunk_content = '\n'.join(current_chunk_lines)
                chunk = TaskChunk(
                    chunk_id=f"chunk_{chunk_index}",
                    content=chunk_content,
                    metadata={
                        'start_line': i - len(current_chunk_lines),
                        'end_line': i - 1,
                        'overlap_ratio': overlap_ratio
                    },
                    estimated_tokens=current_tokens
                )
                chunks.append(chunk)
                
                # 准备下一个块（保留重叠部分）
                if overlap_lines > 0:
                    overlap_start = max(0, len(current_chunk_lines) - overlap_lines)
                    current_chunk_lines = current_chunk_lines[overlap_start:]
                    current_tokens = sum(len(line) // 4 for line in current_chunk_lines)
                else:
                    current_chunk_lines = []
                    current_tokens = 0
                
                chunk_index += 1
            
            current_chunk_lines.append(line)
            current_tokens += line_tokens
        
        # 处理最后一个块
        if current_chunk_lines:
            chunk_content = '\n'.join(current_chunk_lines)
            chunk = TaskChunk(
                chunk_id=f"chunk_{chunk_index}",
                content=chunk_content,
                metadata={
                    'start_line': len(lines) - len(current_chunk_lines),
                    'end_line': len(lines) - 1,
                    'is_last': True
                },
                estimated_tokens=current_tokens
            )
            chunks.append(chunk)
        
        self.logger.info(f"Decomposed content into {len(chunks)} chunks")
        return chunks
    
    def decompose_by_structure(self, content: str) -> List[TaskChunk]:
        """
        按代码结构分解任务
        
        Args:
            content: 代码内容
            
        Returns:
            任务块列表
        """
        chunks = []
        lines = content.split('\n')
        
        current_chunk_lines = []
        current_tokens = 0
        chunk_index = 0
        in_class = False
        in_function = False
        indent_level = 0
        
        for i, line in enumerate(lines):
            line_tokens = len(line) // 4
            stripped_line = line.strip()
            
            # 检测结构边界
            if stripped_line.startswith('class '):
                if current_chunk_lines and current_tokens > 0:
                    # 完成当前块
                    self._create_chunk_from_lines(chunks, current_chunk_lines, chunk_index, i)
                    chunk_index += 1
                    current_chunk_lines = []
                    current_tokens = 0
                in_class = True
                indent_level = len(line) - len(line.lstrip())
            
            elif stripped_line.startswith('def '):
                if not in_class and current_chunk_lines and current_tokens > self.max_chunk_tokens // 2:
                    # 完成当前块
                    self._create_chunk_from_lines(chunks, current_chunk_lines, chunk_index, i)
                    chunk_index += 1
                    current_chunk_lines = []
                    current_tokens = 0
                in_function = True
            
            current_chunk_lines.append(line)
            current_tokens += line_tokens
            
            # 检查是否需要强制分割
            if current_tokens > self.max_chunk_tokens:
                self._create_chunk_from_lines(chunks, current_chunk_lines, chunk_index, i + 1)
                chunk_index += 1
                current_chunk_lines = []
                current_tokens = 0
                in_class = False
                in_function = False
        
        # 处理最后一个块
        if current_chunk_lines:
            self._create_chunk_from_lines(chunks, current_chunk_lines, chunk_index, len(lines))
        
        return chunks
    
    def _create_chunk_from_lines(self, chunks: List[TaskChunk], lines: List[str], 
                                chunk_index: int, end_line: int):
        """从行列表创建块"""
        if not lines:
            return
        
        chunk_content = '\n'.join(lines)
        chunk = TaskChunk(
            chunk_id=f"struct_chunk_{chunk_index}",
            content=chunk_content,
            metadata={
                'start_line': end_line - len(lines),
                'end_line': end_line - 1,
                'structure_based': True
            },
            estimated_tokens=sum(len(line) // 4 for line in lines)
        )
        chunks.append(chunk)


class DistributedProcessor:
    """分布式处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化分布式处理器
        
        Args:
            config: 配置字典
        """
        self.config = config.get('augment', {})
        self.max_workers = min(mp.cpu_count(), 4)  # 限制最大进程数
        self.max_chunk_tokens = 5000
        
        self.decomposer = TaskDecomposer(self.max_chunk_tokens)
        self.logger = logging.getLogger(__name__)
        
        # 结果缓存
        self.result_cache: Dict[str, Any] = {}
    
    async def process_large_request(self, content: str, 
                                  processor_func: Callable[[str], Any],
                                  merge_func: Optional[Callable[[List[Any]], Any]] = None) -> Any:
        """
        处理大型请求
        
        Args:
            content: 内容
            processor_func: 处理函数
            merge_func: 合并函数
            
        Returns:
            处理结果
        """
        start_time = time.time()
        
        # 1. 任务分解
        chunks = self.decompose_task(content)
        self.logger.info(f"Decomposed into {len(chunks)} chunks")
        
        if len(chunks) == 1:
            # 如果只有一个块，直接处理
            return processor_func(chunks[0].content)
        
        # 2. 并行处理
        results = await self._process_chunks_parallel(chunks, processor_func)
        
        # 3. 结果合并
        if merge_func:
            final_result = merge_func([r.result for r in results if r.success])
        else:
            final_result = self._default_merge([r.result for r in results if r.success])
        
        processing_time = time.time() - start_time
        self.logger.info(f"Distributed processing completed in {processing_time:.2f}s")
        
        return final_result
    
    def decompose_task(self, content: str, strategy: str = "auto") -> List[TaskChunk]:
        """
        分解任务
        
        Args:
            content: 内容
            strategy: 分解策略 ("content", "structure", "auto")
            
        Returns:
            任务块列表
        """
        content_tokens = len(content) // 4
        
        if content_tokens <= self.max_chunk_tokens:
            # 不需要分解
            return [TaskChunk(
                chunk_id="single_chunk",
                content=content,
                metadata={'no_decomposition': True},
                estimated_tokens=content_tokens
            )]
        
        if strategy == "auto":
            # 自动选择策略
            if self._looks_like_code(content):
                strategy = "structure"
            else:
                strategy = "content"
        
        if strategy == "structure":
            return self.decomposer.decompose_by_structure(content)
        else:
            return self.decomposer.decompose_by_content(content)
    
    async def _process_chunks_parallel(self, chunks: List[TaskChunk], 
                                     processor_func: Callable[[str], Any]) -> List[ProcessingResult]:
        """并行处理块"""
        results = []
        
        # 使用线程池进行并行处理（避免进程间通信复杂性）
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_chunk = {
                executor.submit(self._process_single_chunk, chunk, processor_func): chunk
                for chunk in chunks
            }
            
            # 收集结果
            for future in as_completed(future_to_chunk):
                chunk = future_to_chunk[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"Chunk {chunk.chunk_id} processing failed: {e}")
                    results.append(ProcessingResult(
                        chunk_id=chunk.chunk_id,
                        result=None,
                        processing_time=0.0,
                        success=False,
                        error_message=str(e)
                    ))
        
        # 按chunk_id排序以保持顺序
        results.sort(key=lambda x: x.chunk_id)
        return results
    
    def _process_single_chunk(self, chunk: TaskChunk, 
                            processor_func: Callable[[str], Any]) -> ProcessingResult:
        """处理单个块"""
        start_time = time.time()
        
        try:
            # 检查缓存
            cache_key = self._get_cache_key(chunk.content)
            if cache_key in self.result_cache:
                result = self.result_cache[cache_key]
                processing_time = time.time() - start_time
                return ProcessingResult(
                    chunk_id=chunk.chunk_id,
                    result=result,
                    processing_time=processing_time,
                    success=True,
                    metadata={'from_cache': True}
                )
            
            # 处理块
            result = processor_func(chunk.content)
            processing_time = time.time() - start_time
            
            # 缓存结果
            self.result_cache[cache_key] = result
            
            return ProcessingResult(
                chunk_id=chunk.chunk_id,
                result=result,
                processing_time=processing_time,
                success=True,
                metadata={'estimated_tokens': chunk.estimated_tokens}
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            return ProcessingResult(
                chunk_id=chunk.chunk_id,
                result=None,
                processing_time=processing_time,
                success=False,
                error_message=str(e)
            )
    
    def _default_merge(self, results: List[Any]) -> Any:
        """默认合并策略"""
        if not results:
            return None
        
        if len(results) == 1:
            return results[0]
        
        # 如果结果是字符串，直接连接
        if all(isinstance(r, str) for r in results):
            return '\n'.join(results)
        
        # 如果结果是列表，合并列表
        if all(isinstance(r, list) for r in results):
            merged = []
            for r in results:
                merged.extend(r)
            return merged
        
        # 如果结果是字典，合并字典
        if all(isinstance(r, dict) for r in results):
            merged = {}
            for r in results:
                merged.update(r)
            return merged
        
        # 默认返回列表
        return results
    
    def _looks_like_code(self, content: str) -> bool:
        """判断内容是否像代码"""
        code_indicators = [
            'def ', 'class ', 'import ', 'from ',
            'if __name__', 'return ', 'yield ',
            '#!/usr/bin', '# -*- coding'
        ]
        
        lines = content.split('\n')[:10]  # 只检查前10行
        code_line_count = 0
        
        for line in lines:
            if any(indicator in line for indicator in code_indicators):
                code_line_count += 1
        
        return code_line_count >= 2
    
    def _get_cache_key(self, content: str) -> str:
        """生成缓存键"""
        return hashlib.md5(content.encode()).hexdigest()
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'max_workers': self.max_workers,
            'max_chunk_tokens': self.max_chunk_tokens,
            'cache_size': len(self.result_cache),
            'cache_hit_ratio': 0.0  # 简化实现
        }
    
    def clear_cache(self):
        """清理缓存"""
        self.result_cache.clear()
        self.logger.info("Processing cache cleared")
