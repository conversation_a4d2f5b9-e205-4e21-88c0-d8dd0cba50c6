#!/usr/bin/env python3
"""
Augment系统启动脚本

一键启动Augment prompt length exceeded解决方案的所有组件。
"""

import os
import sys
import subprocess
import time
import argparse
from pathlib import Path


def check_dependencies():
    """检查依赖项"""
    print("🔍 检查依赖项...")
    
    required_packages = [
        'pydantic',
        'pydantic-settings', 
        'streamlit',
        'plotly',
        'pandas',
        'numpy'
    ]
    
    optional_packages = [
        'tiktoken',
        'scikit-learn'
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_required.append(package)
    
    for package in optional_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_optional.append(package)
    
    if missing_required:
        print(f"❌ 缺少必需依赖: {', '.join(missing_required)}")
        print("请运行: pip install " + " ".join(missing_required))
        return False
    
    if missing_optional:
        print(f"⚠️ 缺少可选依赖: {', '.join(missing_optional)}")
        print("建议运行: pip install " + " ".join(missing_optional))
    
    print("✅ 依赖项检查完成")
    return True


def setup_directories():
    """设置必要的目录"""
    print("📁 设置目录结构...")
    
    directories = [
        'data',
        'logs',
        'cache',
        '.tasks'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ {directory}/")
    
    print("✅ 目录结构设置完成")


def check_config():
    """检查配置文件"""
    print("⚙️ 检查配置文件...")
    
    config_file = Path('config/augment.yaml')
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        print("请确保已创建 config/augment.yaml 配置文件")
        return False
    
    print("✅ 配置文件检查完成")
    return True


def run_tests():
    """运行测试"""
    print("🧪 运行集成测试...")
    
    test_file = Path('tests/test_augment_integration.py')
    if not test_file.exists():
        print("⚠️ 测试文件不存在，跳过测试")
        return True
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            str(test_file), '-v', '--tb=short'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 测试通过")
            return True
        else:
            print("❌ 测试失败")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 测试超时，跳过")
        return True
    except Exception as e:
        print(f"⚠️ 测试运行出错: {e}")
        return True


def run_demo():
    """运行演示"""
    print("🎬 运行系统演示...")
    
    demo_file = Path('examples/augment_integration_example.py')
    if not demo_file.exists():
        print("⚠️ 演示文件不存在，跳过演示")
        return True
    
    try:
        result = subprocess.run([
            sys.executable, str(demo_file)
        ], timeout=120)
        
        if result.returncode == 0:
            print("✅ 演示运行成功")
            return True
        else:
            print("❌ 演示运行失败")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 演示超时")
        return False
    except Exception as e:
        print(f"❌ 演示运行出错: {e}")
        return False


def start_monitoring_dashboard():
    """启动监控仪表板"""
    print("📊 启动监控仪表板...")
    
    dashboard_file = Path('src/streamlit_app/pages/augment_monitor.py')
    if not dashboard_file.exists():
        print("❌ 监控仪表板文件不存在")
        return False
    
    try:
        print("🚀 启动Streamlit监控界面...")
        print("📱 访问地址: http://localhost:8501")
        print("⏹️ 按 Ctrl+C 停止服务")
        
        subprocess.run([
            'streamlit', 'run', str(dashboard_file),
            '--server.port', '8501',
            '--server.address', '127.0.0.1'
        ])
        
        return True
        
    except KeyboardInterrupt:
        print("\n⏹️ 监控仪表板已停止")
        return True
    except Exception as e:
        print(f"❌ 启动监控仪表板失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Augment系统启动脚本')
    parser.add_argument('--skip-tests', action='store_true', help='跳过测试')
    parser.add_argument('--skip-demo', action='store_true', help='跳过演示')
    parser.add_argument('--dashboard-only', action='store_true', help='只启动监控仪表板')
    parser.add_argument('--check-only', action='store_true', help='只进行检查，不启动服务')
    
    args = parser.parse_args()
    
    print("🚀 Augment Prompt Length Exceeded 解决方案")
    print("=" * 60)
    
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)
    
    # 设置目录
    setup_directories()
    
    # 检查配置
    if not check_config():
        sys.exit(1)
    
    if args.check_only:
        print("✅ 系统检查完成，所有组件就绪")
        return
    
    if args.dashboard_only:
        start_monitoring_dashboard()
        return
    
    # 运行测试
    if not args.skip_tests:
        if not run_tests():
            print("⚠️ 测试失败，但继续启动系统")
    
    # 运行演示
    if not args.skip_demo:
        if not run_demo():
            print("⚠️ 演示失败，但继续启动系统")
    
    print("\n🎉 Augment系统启动完成！")
    print("=" * 60)
    print("📋 可用功能:")
    print("   1. Token预算管理 - 实时监控和智能截断")
    print("   2. 分层上下文管理 - 三层架构优化")
    print("   3. 优先级算法 - 多因子重要性评分")
    print("   4. 智能截断 - 结构感知压缩")
    print("   5. 分层缓存 - 高效存储和检索")
    print("   6. 分布式处理 - 并行任务处理")
    print("   7. 代码压缩 - 语义压缩引擎")
    print("   8. 智能过滤 - AI驱动文件过滤")
    print()
    print("🔧 使用方式:")
    print("   - 监控界面: python start_augment_system.py --dashboard-only")
    print("   - 集成示例: python examples/augment_integration_example.py")
    print("   - 配置文件: config/augment.yaml")
    print()
    print("📚 文档: README_AUGMENT_OPTIMIZATION.md")
    print()
    
    # 询问是否启动监控仪表板
    try:
        response = input("是否启动监控仪表板? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            start_monitoring_dashboard()
    except KeyboardInterrupt:
        print("\n👋 再见！")


if __name__ == "__main__":
    main()
